import axios from "axios";
require("dotenv").config();

const BaseUrl = process.env.NEXT_PUBLIC_Base_URL;

const getAvailableAccApi = async (PayinPaymentMethod) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const res = await axios({
    url: `${BaseUrl}/get-user-choice-payment-fields-data/?payment_method=${PayinPaymentMethod}`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return res;
};

export default getAvailableAccApi;
