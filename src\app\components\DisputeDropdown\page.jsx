"use client";
import Image from "next/image";
import { useEffect, useState, useRef } from "react";
import styles from "./disputeDrop.module.css";
import tick from "../../../../public/assets/tick.png";
import { helpDeskPostApi } from "@/app/api/helpDeskApis/helpDesk";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  addDisputeListApi,
  getDisputesOrderDropDownApi,
} from "@/app/api/disputeApi's/dispute";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const DisputeDropdown = ({ title, dispute, selectedId }) => {
  const [hide, setHide] = useState(false);
  const [hideReason, setHideReason] = useState(false);
  const [uploadMark, setUploadMark] = useState("");
  const [comment, setComment] = useState("");
  const [uploadEvidence, setUploadEvidence] = useState("");
  const [queryTitle, setQueryTitle] = useState("");
  const [loadDisputeDrop, setLoadDisputeDrop] = useState([]);
  const [selectedOrderNumber, setSelectedOrderNumber] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const handleReasonContainer = () => {
    setHideReason(!hideReason);
  };

  const handleDisputeComment = (e) => {
    const value = e.target.value;
    setComment(value);

    // Clear error when user starts typing
    if (errors.comment) {
      setErrors(prev => ({ ...prev, comment: '' }));
    }
  };

  const handleUploadMark = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, upload: 'Please upload a valid image or PDF file' }));
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, upload: 'File size must be less than 5MB' }));
        return;
      }

      setUploadEvidence(file);
      setUploadMark(file);
      setErrors(prev => ({ ...prev, upload: '' }));
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      handleUploadMark({ target: { files: [file] } });
    }
  };

  const validateFields = () => {
    const newErrors = {};

    if (!selectedOrderNumber || selectedOrderNumber === "-1") {
      newErrors.orderNumber = "Please select an order number";
    }

    if (!uploadEvidence) {
      newErrors.upload = "Please upload evidence";
    }

    if (!comment.trim()) {
      newErrors.comment = "Please provide a comment";
    } else if (comment.trim().length < 10) {
      newErrors.comment = "Comment must be at least 10 characters";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      toast.error("Please fix the errors below", { toastId: "validation-error" });
      return false;
    }

    return true;
  };

  const addHelpQuery = async () => {
    if (!validateFields()) {
      return;
    }

    setIsSubmitting(true);

    const formData = new FormData();
    formData.append("disput_title", selectedId);
    formData.append("upload_evidence", uploadEvidence);
    formData.append("comments", comment);
    formData.append("order_number", selectedOrderNumber);

    try {
      const response = await customFetchWithToken.post(
        "/dispute-list/",
        formData
      );

      toast.success(response.data.message, { toastId: "success-toast" });

      // Reset form on success
      setComment("");
      setUploadEvidence("");
      setUploadMark("");
      setSelectedOrderNumber("");
      setErrors({});
      // Clear the file input value
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

    } catch (err) {
      console.error(err);
      const errorMessage = err.response?.data?.message || "An error occurred while submitting the dispute";
      toast.error(errorMessage, { toastId: "error-toast" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const loadDisputeDropdown = async () => {
    try {
      const res = await customFetchWithToken.get("/order-dispute-list/");
      setLoadDisputeDrop(res.data.data);
    } catch (error) {
      console.error(error);
      toast.error("Failed to load order numbers", { toastId: "load-error" });
    }
  };

  const handleOrderDropdown = (e) => {
    setSelectedOrderNumber(e.target.value);

    // Clear error when user selects an option
    if (errors.orderNumber) {
      setErrors(prev => ({ ...prev, orderNumber: '' }));
    }
  };

  const getCharacterCount = () => {
    return comment.length;
  };

  const getCharacterCountColor = () => {
    const count = getCharacterCount();
    if (count > 180) return '#ef4444'; // Red
    if (count > 150) return '#f59e0b'; // Orange
    return '#6b7280'; // Gray
  };

  useEffect(() => {
    loadDisputeDropdown();
  }, []);
  return (
    <div className={styles.disputeFormContainer}>
      {/* Form Header */}
      <div className={styles.formHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerIcon}>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div className={styles.headerText}>
            <h3 className={styles.formTitle}>{title}</h3>
            <p className={styles.formDescription}>
              Please provide the required information to submit your dispute
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className={styles.formContent}>
        <form className={styles.disputeForm} onSubmit={(e) => e.preventDefault()}>

          {/* Order Number Field */}
          <div className={styles.fieldGroup}>
            <label htmlFor="order" className={styles.fieldLabel}>
              <svg className={styles.labelIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
              </svg>
              Order Number
              <span className={styles.required}>*</span>
            </label>
            <div className={styles.selectWrapper}>
              <select
                className={`${styles.selectField} ${errors.orderNumber ? styles.fieldError : ''}`}
                name="currencyPayout"
                id="order"
                value={selectedOrderNumber}
                onChange={handleOrderDropdown}
                aria-describedby={errors.orderNumber ? "order-error" : undefined}
              >
                <option value="-1">Select an order number</option>
                {loadDisputeDrop.map((el, index) => (
                  <option key={index} value={el.order_number}>
                    {el.order_number}
                  </option>
                ))}
              </select>
              <div className={styles.selectIcon}>
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
            {errors.orderNumber && (
              <div id="order-error" className={styles.errorMessage} role="alert">
                <svg className={styles.errorIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                {errors.orderNumber}
              </div>
            )}
          </div>

          {/* File Upload Field */}
          <div className={styles.fieldGroup}>
            <label htmlFor="upload" className={styles.fieldLabel}>
              <svg className={styles.labelIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
              Upload Evidence
              <span className={styles.required}>*</span>
            </label>
            <div
              className={`${styles.uploadArea} ${dragActive ? styles.dragActive : ''} ${errors.upload ? styles.fieldError : ''}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                ref={fileInputRef}
                id="upload"
                type="file"
                className={`${styles.fileInput} ${uploadMark ? styles.disabled : ''}`}
                onChange={handleUploadMark}
                accept="image/*,.pdf"
                aria-describedby={errors.upload ? "upload-error" : "upload-help"}
              />

              <div className={styles.uploadContent}>
                {uploadMark ? (
                  <div className={styles.uploadSuccess}>
                    <div className={styles.successIcon}>
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div className={styles.uploadedFileInfo}>
                      <span className={styles.fileName}>{uploadMark.name}</span>
                      <span className={styles.fileSize}>
                        {(uploadMark.size / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                    <button
                      type="button"
                      className={styles.removeFileBtn}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setUploadMark("");
                        setUploadEvidence("");
                        // Clear the file input value to allow re-selecting the same file
                        if (fileInputRef.current) {
                          fileInputRef.current.value = "";
                        }
                      }}
                      aria-label="Remove file"
                    >
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <div className={styles.uploadPrompt}>
                    <div className={styles.uploadIcon}>
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    <div className={styles.uploadText}>
                      <span className={styles.uploadPrimary}>Click to upload</span>
                      <span className={styles.uploadSecondary}>or drag and drop</span>
                    </div>
                    <div className={styles.uploadHint}>
                      PNG, JPG, GIF or PDF (max. 5MB)
                    </div>
                  </div>
                )}
              </div>
            </div>

            {errors.upload && (
              <div id="upload-error" className={styles.errorMessage} role="alert">
                <svg className={styles.errorIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                {errors.upload}
              </div>
            )}

            {!errors.upload && (
              <div id="upload-help" className={styles.helpText}>
                Upload screenshots, documents, or other evidence related to your dispute
              </div>
            )}
          </div>

          {/* Comment Field */}
          <div className={styles.fieldGroup}>
            <label htmlFor="comments" className={styles.fieldLabel}>
              <svg className={styles.labelIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Description
              <span className={styles.required}>*</span>
            </label>
            <div className={styles.textareaWrapper}>
              <textarea
                className={`${styles.textareaField} ${errors.comment ? styles.fieldError : ''}`}
                id="comments"
                rows="6"
                placeholder="Please provide a detailed description of your dispute. Include relevant information such as transaction details, timeline, and any issues encountered..."
                maxLength="200"
                value={comment}
                onChange={handleDisputeComment}
                aria-describedby={errors.comment ? "comment-error" : "comment-help"}
              />
              <div className={styles.characterCount}>
                <span style={{ color: getCharacterCountColor() }}>
                  {getCharacterCount()}/200
                </span>
              </div>
            </div>

            {errors.comment && (
              <div id="comment-error" className={styles.errorMessage} role="alert">
                <svg className={styles.errorIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                {errors.comment}
              </div>
            )}

            {!errors.comment && (
              <div id="comment-help" className={styles.helpText}>
                Provide clear details about your dispute to help us resolve it quickly
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className={styles.submitSection}>
            <button
              type="button"
              className={`${styles.submitBtn} ${isSubmitting ? styles.submitting : ''}`}
              onClick={addHelpQuery}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className={styles.spinner}></div>
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <svg className={styles.submitIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                  <span>Submit Dispute</span>
                </>
              )}
            </button>

            <div className={styles.submitNote}>
              <svg className={styles.noteIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Your dispute will be reviewed within 24-48 hours</span>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DisputeDropdown;
