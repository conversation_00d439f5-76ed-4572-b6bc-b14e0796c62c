"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./history.module.css";
import HistoryCard from "../../components/HistoryCard/page";
import HistoryCardMobView from "../../components/HistoryCardMobView/page";
import Layout from "../../components/Layout/page";
import ReactPaginate from "react-paginate";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import LoadingSpinner from "../../components/LoadingSpinner/page";
import { useRouter } from "next/navigation";

const HistoryPage = () => {
  const router = useRouter();
  const [noOfRecords, setNoOfRecords] = useState(0);
  const itemsPerPage = 10;
  const pageCount = Math.ceil(noOfRecords / itemsPerPage);

  const [currentPage, setCurrentPage] = useState(0);
  const [page, setPage] = useState(1);
  const authTokenRef = useRef(null);

  const [tradeReqArr, setTradeReqArr] = useState([]);
  const [tradeStatus, setTradeStatus] = useState("");
  const [tabHeader, setTabHeader] = useState("");
  const [searchByOrderNo, setSearchByOrderNo] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

console.log("tradeReqArr", tradeReqArr);

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const searchByOrderNoHandler = async (e) => {
    setLoading(true);
    setError(null);
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    setSearchByOrderNo(inputValue);
    setCurrentPage(0);
    setPage(1);

    try {
      setTimeout(async () => {
        try {
          const res = await customFetchWithToken.get(
            `/get-trade-request/?order_number=${e.target.value}&status=`
          );
          setTradeReqArr(res.data.results);
        } catch (searchError) {
          setError("Failed to search orders. Please try again.");
          console.error("Search error:", searchError);
        }
      }, 2000);
    } catch (error) {
      setError("An unexpected error occurred.");
      console.error("Handler error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTradeReqByStatus = async (e) => {
    setLoading(true);
    setError(null);
    setTradeStatus(e.target.value);
    setCurrentPage(0);
    setPage(1);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?status=${e.target.value}`
      );
      setTradeReqArr(res.data.results);
      setTabHeader(`${e.target.value || "All"} Status Results`);
    } catch (error) {
      setError("Failed to filter by status. Please try again.");
      console.error("Status filter error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAllTradeReq = async () => {
    setLoading(true);
    setError(null);
    setTradeStatus("All Status");
    setSearchByOrderNo("");
    setCurrentPage(0);
    setPage(1);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?page=${page}`
      );
      setTradeReqArr(res.data.results);
      setNoOfRecords(res.data.count);
      setTabHeader("All Results");
    } catch (error) {
      setError("Failed to load trade requests. Please try again.");
      console.error("Load all trades error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSenderTradeReq = async () => {
    setLoading(true);
    setError(null);
    setTradeStatus("All Status");
    setSearchByOrderNo("");
    setCurrentPage(0);
    setPage(1);

    try {
      const res = await customFetchWithToken.get(
        "/get-trade-request/?type=sender"
      );
      setTradeReqArr(res.data.results);
      setTabHeader("Sender Results");
      setNoOfRecords(res.data.count);
    } catch (error) {
      setError("Failed to load sender trades. Please try again.");
      console.error("Load sender trades error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePeerTradeReq = async () => {
    setLoading(true);
    setError(null);
    setTradeStatus("All Status");
    setSearchByOrderNo("");
    setCurrentPage(0);
    setPage(1);
    try {
      const res = await customFetchWithToken.get(
        "/get-trade-request/?type=peer"
      );
      setNoOfRecords(res.data.count);
      setTradeReqArr(res.data.results);
      setTabHeader("Peer Results");
    } catch (error) {
      setError("Failed to load peer trades. Please try again.");
      console.error("Load peer trades error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageClick = async (event) => {
    const selectedPage = event.selected;
    setCurrentPage(selectedPage);
    setPage(selectedPage + 1);
    setLoading(true);
    setError(null);

    try {
      const resData = await customFetchWithToken.get(
        `/get-trade-request/?page=${selectedPage + 1}`
      );
      const PassData = resData.data.results;

      if (PassData.length < 1) {
        setError("No data available for this page.");
      } else {
        setNoOfRecords(resData.data.count);
        setTradeReqArr(PassData);
      }
    } catch (error) {
      setError("Failed to load page data. Please try again.");
      console.error("Pagination error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleAllTradeReq();
  }, []);

  const historyTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Trade History</h1>
      <p className={styles.pageSubtitle}>
        View your complete transaction and trading history
      </p>
    </div>
  );

  return (
    <div className={styles.historyPageContainer}>
      <Layout title={historyTitle}>
        <div className={styles.contentWrapper}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.pageHeader}>
            <h1 className={styles.pageTitle}>Trade History</h1>
            <p className={styles.pageSubtitle}>
              View your complete transaction and trading history
            </p>
          </div>

          <div className={styles.filtersSection}>
            <div className={styles.searchControls}>
              <div className={styles.searchInputGroup}>
                <label htmlFor="orderSearch" className={styles.searchLabel}>
                  Search by Order Number
                </label>
                <input
                  id="orderSearch"
                  type="text"
                  maxLength={260}
                  className={styles.searchInput}
                  placeholder="Enter order number..."
                  value={searchByOrderNo}
                  onChange={searchByOrderNoHandler}
                  aria-describedby="orderSearchHelp"
                />
                <small id="orderSearchHelp" className={styles.inputHelp}>
                  Search for specific orders using their unique number
                </small>
              </div>

              <div className={styles.statusFilterGroup}>
                <label htmlFor="statusFilter" className={styles.filterLabel}>
                  Filter by Status
                </label>
                <select
                  id="statusFilter"
                  name="search"
                  className={styles.statusSelect}
                  onChange={handleTradeReqByStatus}
                  value={tradeStatus}
                  aria-describedby="statusFilterHelp"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="rejected">Rejected</option>
                  <option value="expired">Expired</option>
                  <option value="ongoing">Ongoing</option>
                </select>
                <small id="statusFilterHelp" className={styles.inputHelp}>
                  Filter trades by their current status
                </small>
              </div>
            </div>

            <div className={styles.actionButtons}>
              <button
                className={`${styles.filterButton} ${styles.allButton}`}
                onClick={handleAllTradeReq}
                disabled={loading}
                aria-label="Show all trades"
              >
                <span className={styles.buttonIcon}>📊</span>
                All Trades
              </button>
              <button
                className={`${styles.filterButton} ${styles.peerButton}`}
                onClick={handlePeerTradeReq}
                disabled={loading}
                aria-label="Show peer trades"
              >
                <span className={styles.buttonIcon}>🤝</span>
                Peer Trades
              </button>
              <button
                className={`${styles.filterButton} ${styles.senderButton}`}
                onClick={handleSenderTradeReq}
                disabled={loading}
                aria-label="Show sender trades"
              >
                <span className={styles.buttonIcon}>📤</span>
                Sender Trades
              </button>
            </div>
          </div>

          {tabHeader && (
            <div className={styles.resultsHeader}>
              <h2 className={styles.resultsTitle}>{tabHeader}</h2>
              <span className={styles.resultsCount}>
                {noOfRecords} {noOfRecords === 1 ? "result" : "results"}
              </span>
            </div>
          )}

          {error && (
            <div className={styles.errorMessage} role="alert">
              <span className={styles.errorIcon}>⚠️</span>
              {error}
            </div>
          )}
          <div className={styles.contentSection}>
            {loading ? (
              <div className={styles.loadingContainer}>
                <LoadingSpinner text="Loading trades..." />
                <p className={styles.loadingText}>Please wait while we fetch your trade history</p>
              </div>
            ) : (
              <>
                {pageCount > 1 && (
                  <div className={styles.paginationTop}>
                    <ReactPaginate
                      previousLabel={"← Previous"}
                      nextLabel={"Next →"}
                      breakLabel={"..."}
                      breakClassName={styles.paginationBreak}
                      activeClassName={styles.paginationActive}
                      pageCount={pageCount}
                      onPageChange={handlePageClick}
                      forcePage={currentPage}
                      marginPagesDisplayed={2}
                      pageRangeDisplayed={2}
                      containerClassName={styles.paginationContainer}
                      pageClassName={styles.paginationPage}
                      pageLinkClassName={styles.paginationLink}
                      previousClassName={styles.paginationPrev}
                      previousLinkClassName={styles.paginationPrevLink}
                      nextClassName={styles.paginationNext}
                      nextLinkClassName={styles.paginationNextLink}
                      breakLinkClassName={styles.paginationBreakLink}
                      renderOnZeroPageCount={null}
                      ariaLabelBuilder={(page, selected) =>
                        selected ? `Current page ${page}` : `Go to page ${page}`
                      }
                    />
                  </div>
                )}

                <div className={styles.tradesContainer}>
                  {tradeReqArr?.length > 0 ? (
                    <>
                      <div className={styles.desktopTradesGrid}>
                        {tradeReqArr.map((el) => (
                          <HistoryCard
                            key={el?.id}
                            id={el?.listing?.id}
                            orderNo={el?.order_number}
                            dateCreated={el?.created_date}
                            status={el?.order_status}
                            trade_amount={el?.trade_amount}
                            availableLiquidity={el?.available_liquidity}
                            max_liquidity={el?.max_liquidity}
                            min_liquidity={el?.min_liquidity}
                            indicative_fx_rate={el?.indicative_fx_rate}
                            listing_id={el?.listing.id}
                            payin_currency_id__currency_code={el?.payin_currency}
                            payin_option_id__payment_method={el?.payin_option}
                            payout_currency_id__currency_code={el?.payout_currency}
                            payout_option_id__payment_method={el?.payout_option}
                            peer_id={el?.peer_id.id}
                            terms={el?.listing.terms_and_conditions}
                            finalTradeFee={el?.listing.final_trade_fee}
                          />
                        ))}
                      </div>

                      <div className={styles.mobileTradesGrid}>
                        {tradeReqArr.map((el) => (
                          <HistoryCardMobView
                            key={el.id}
                            id={el?.listing?.id}
                            orderNo={el?.order_number}
                            dateCreated={el?.created_date}
                            status={el?.order_status}
                            trade_amount={el?.trade_amount}
                            availableLiquidity={el?.available_liquidity}
                            max_liquidity={el?.max_liquidity}
                            min_liquidity={el?.min_liquidity}
                            indicative_fx_rate={el?.indicative_fx_rate}
                            listing_id={el?.listing.id}
                            payin_currency_id__currency_code={el?.payin_currency}
                            payin_option_id__payment_method={el?.payin_option}
                            payout_currency_id__currency_code={el?.payout_currency}
                            payout_option_id__payment_method={el?.payout_option}
                            peer_id={el?.peer_id.id}
                            terms={el?.listing.terms_and_conditions}
                            finalTradeFee={el?.listing.final_trade_fee}
                          />
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className={styles.emptyState}>
                      <div className={styles.emptyStateIcon}>📊</div>
                      <h3 className={styles.emptyStateTitle}>No trades found</h3>
                      <p className={styles.emptyStateMessage}>
                        {searchByOrderNo || tradeStatus
                          ? "Try adjusting your search criteria or filters"
                          : "You haven't made any trades yet. Start trading to see your history here."}
                      </p>
                      {(searchByOrderNo || tradeStatus) && (
                        <button
                          className={styles.clearFiltersButton}
                          onClick={handleAllTradeReq}
                        >
                          Clear Filters
                        </button>
                      )}
                    </div>
                  )}
                </div>

                {pageCount > 1 && tradeReqArr?.length > 0 && (
                  <div className={styles.paginationBottom}>
                    <ReactPaginate
                      previousLabel={"← Previous"}
                      nextLabel={"Next →"}
                      breakLabel={"..."}
                      breakClassName={styles.paginationBreak}
                      activeClassName={styles.paginationActive}
                      pageCount={pageCount}
                      onPageChange={handlePageClick}
                      forcePage={currentPage}
                      marginPagesDisplayed={2}
                      pageRangeDisplayed={2}
                      containerClassName={styles.paginationContainer}
                      pageClassName={styles.paginationPage}
                      pageLinkClassName={styles.paginationLink}
                      previousClassName={styles.paginationPrev}
                      previousLinkClassName={styles.paginationPrevLink}
                      nextClassName={styles.paginationNext}
                      nextLinkClassName={styles.paginationNextLink}
                      breakLinkClassName={styles.paginationBreakLink}
                      renderOnZeroPageCount={null}
                    />
                  </div>
                )}
              </>
            )}
          </div>

        </div>
      </Layout>
    </div>
  );
};

export default HistoryPage;
