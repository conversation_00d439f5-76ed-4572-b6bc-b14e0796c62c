"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./addrecipient.module.css";

import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  getCountryTOapi,
  getCurrencyListByCountry,
} from "../../api/getCountries/getCountriesApi";
import { addRecipientApi } from "@/app/api/addRecipientApi's/addRecipientapi";
import { getAddPayFields } from "../../api/userAddPayment/userAddPay";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = () => {
  const [selectedOption, setSelectedOption] = useState("");
  const [citizenship, setCitizenship] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [dob, setDob] = useState("");
  const [dateOfIncorporation, setDateOfIncorporation] = useState("");
  const [shareHolderName, setShareHolderName] = useState("");
  const [email, setEmail] = useState("");
  const [shareHolderEmail, setShareHolderEmail] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [countryTo, setCountryTo] = useState([]);
  const [currencyPayout, setCurrencyPayout] = useState("");
  const [messagePayout, setMessagePayout] = useState("");
  const [dataPayout, setDataPayout] = useState([]);
  // const [currencyAccepted, setCurrencyAccepted] = useState("");
  const [payMethodPayout, setPayMethodPayout] = useState("");
  const [AccUpiID, setAccUpiID] = useState("");
  const [bankAccHolderName, setBankAccHolderName] = useState("");
  const [ifscCode, setIfscCode] = useState("");
  const [totalFields, setTotalFields] = useState([]);
  const [accUpiIDs, setAccUpiIDs] = useState([]);
  // const [accUpiIDs, setAccUpiIDs] = useState(
  //   Array(totalFields.length).fill("")
  // );
  const authTokenRef = useRef(null);

  const getPaymentFields = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/user-payment-fields/?payment_method=${payMethodPayout}&currency=${currencyPayout}`
      );

      setTotalFields(res.data.data);
    } catch (error) {
      console.error(error);
    }
  };

  if (typeof window !== "undefined") {
    const token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 18 }, (_, index) => currentYear - index);

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
  const handleDropdownChange = (event) => {
    setSelectedOption(event.target.value);
  };

  // temp
  const handleEmailChange = (e) => {
    const inputValue = e.target.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(inputValue)) {
      // If the input value doesn't match the email format, you can handle it here
      // For example, display an error message or prevent form submission

      // Optionally, you can clear the email value to prevent storing invalid email
      setEmail("");
    } else {
      // If the input value matches the email format, you can proceed with storing the email value
      setEmail(inputValue);
    }
  };

  const loadCountryTo = async () => {
    const res = await customFetchWithToken("/country-list/?flag=to");
    setCountryTo(res.data.data);
    loadCurrencyToResult();
  };

  // paymentMethod code 👇

  const fetchPaymentMethodspayout = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyPayout}`
      );

      const data = await resCurrency.json();
      setDataPayout(data.data);
      setMessagePayout(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const handlePayMethodPayout = (event) => {
    setPayMethodPayout(event.target.value);
  };

  const handleCompanyName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setCompanyName(inputValue);
  };

  const handleSetFirstName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z ]/g, "");
    setFirstName(inputValue);
  };

  const handleSetLastName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z ]/g, "");
    setLastName(inputValue);
  };

  const handlePersonDOB = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-]/g, "");
    setDob(inputValue);
  };

  const handlePersonEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setEmail(inputValue);
  };

  const handleCompanyEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setShareHolderEmail(inputValue);
  };

  const handleShareHolderName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z ]/g, "");
    setShareHolderName(inputValue);
  };
  const handleShareHolderDateOfIncorporation = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-]/g, "");
    setDateOfIncorporation(inputValue);
  };

  const addRecipientDataBusiness = {
    email: shareHolderEmail,
    type: selectedOption,
    payout_option: payMethodPayout,
    ubo_shareholder_name: shareHolderName,
    ubo_shareholder_date_of_incorporation: dateOfIncorporation,
    country: citizenship, //
    currency_payout: currencyPayout,
    company_name: companyName,
    data: accUpiIDs,
  };

  const addRecipientDataPerson = {
    type: selectedOption,
    country: citizenship, //
    email: email,
    currency_payout: currencyPayout,
    payout_option: payMethodPayout,
    firstname: firstName,
    lastname: lastName,
    dob: dob,
    data: accUpiIDs,
  };

  const submitRecipientHandler = async (e) => {
    e.preventDefault();

    const requiredLabel = {
      citizenship: "Country Name",
      firstName: "First Name",
      lastName: "Last Name",
      dob: "Date of Birth",
      email: "Contact Email",
      currencyPayout: "Currency of payout",
      payMethodPayout: "Select Payment Method (TO)",
      bankAccHolderName: "Bank Accound Holder's Name",
      ifscCode: "IFSC Code",
      shareHolderName: "UBO Shareholder Name",
      shareHolderEmail: "Contact Email",
      dateOfIncorporation: "UBO Shareholder Date of Incorporation",
    };

    const handleInputLogic = () => {
      if (selectedOption === "Person") {
        if (!citizenship) {
          toast.error(`Please select ${requiredLabel.citizenship} to proceed`);
          return;
        }
        if (!firstName) {
          toast.error(`Please enter ${requiredLabel.firstName} to proceed`);
          return;
        }
        if (!lastName) {
          toast.error(`Please enter ${requiredLabel.lastName} to proceed`);
          return;
        }
        if (!dob) {
          toast.error(`Please enter ${requiredLabel.dob} to proceed`);
          return;
        }
        if (!email) {
          toast.error(`Please enter ${requiredLabel.email} to proceed`);
          return;
        }
        if (!currencyPayout) {
          toast.error(
            `Please enter ${requiredLabel.currencyPayout} to proceed`
          );
          return;
        }
        if (!payMethodPayout) {
          toast.error(
            `Please enter ${requiredLabel.payMethodPayout} to proceed`
          );
          return;
        }
      } else if (selectedOption === "Business") {
        if (!citizenship) {
          toast.error(`Please select ${requiredLabel.citizenship} to proceed`);
          return;
        }
        if (!shareHolderEmail) {
          toast.error(
            `Please select ${requiredLabel.shareHolderEmail} to proceed`
          );
          return;
        }
        if (!shareHolderName) {
          toast.error(
            `Please select ${requiredLabel.shareHolderName} to proceed`
          );
          return;
        }
        if (!dateOfIncorporation) {
          toast.error(
            `Please select ${requiredLabel.dateOfIncorporation} to proceed`
          );
          return;
        }
        if (!currencyPayout) {
          toast.error(
            `Please select ${requiredLabel.currencyPayout} to proceed`
          );
          return;
        }
        if (!payMethodPayout) {
          toast.error(
            `Please select ${requiredLabel.payMethodPayout} to proceed`
          );
          return;
        }
      }
    };
    handleInputLogic();
    if (totalFields.length !== accUpiIDs.length) {
      return toast.error("Enter all the payment input fields to proceed");
    }
    try {
      if (selectedOption === "Business") {
        const res = await customFetchWithToken.post(
          "/add-recipient/",
          addRecipientDataBusiness
        );

        toast.success(res.data.message);
      } else if (selectedOption === "Person") {
        const res = await customFetchWithToken.post(
          "/add-recipient/",
          addRecipientDataPerson
        );

        toast.success(res.data.message);
      }
      // setSelectedOption(null);
      setCitizenship("");
      setFirstName("");
      setLastName("");
      setDob("");
      setDateOfIncorporation("");
      setShareHolderName("");
      setEmail("");
      setShareHolderEmail("");
      setCurrencyPayout("");
      setMessagePayout("");
      setDataPayout([]);
      setPayMethodPayout("");
      setAccUpiID("");
      setBankAccHolderName("");
      setIfscCode("");
      setCompanyName("");
      setTotalFields([]);
      setAccUpiIDs([]);
    } catch (error) {
      console.log(error);
      // error.response.data.message.message.map((el) => toast.error(el));
      error.response.data.message.data.map((el) => toast.error(el));
      // toast.error(error.response.data.message.message[0]);
    }
  };

  const loadCurrencyToResult = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/select-country-get-currency?country=${citizenship}`
      );

      setCurrencyPayout(res.data.data.currency__currency_code);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    loadCurrencyToResult();
  }, [citizenship]);

  useEffect(() => {
    fetchPaymentMethodspayout();
  }, [currencyPayout]);

  useEffect(() => {
    loadCountryTo();
  }, []);
  // paymentMethod code 👆
  useEffect(() => {
    getPaymentFields();
  }, [payMethodPayout]);

  return (
    <>
      <div className={styles.rightContainerBody}>
        <div className={styles.body}>
          <div className={styles.secondformWrapper}>
            <div className={styles.sec_firstName}>
              <div className={styles.firstNameLabel}>Type</div>
              <div className={styles.firstNameInput}>
                <select value={selectedOption} onChange={handleDropdownChange}>
                  <option value="select">select </option>
                  <option value="Person">Person</option>
                  <option value="Business">Business</option>
                </select>
              </div>
            </div>
            {selectedOption === "Person" && (
              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>Country name</div>
                <div className={styles.firstNameInput}>
                  <select
                    name="citizenship"
                    value={citizenship}
                    onChange={(e) => setCitizenship(e.target.value)}
                    id="citizenship"
                    required
                  >
                    <option value="-1">Please select a Country</option>
                    {countryTo.map((country) => (
                      <option key={country.id} value={country.country_name}>
                        {country.country_name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
            {selectedOption === "Business" && (
              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>Company Name</div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="companyName"
                    maxLength={260}
                    value={companyName}
                    onChange={handleCompanyName}
                    required
                  />
                </div>
              </div>
            )}
          </div>
          {/* names */}
          {selectedOption === "Person" && (
            <div className={styles.thirdformWrapper}>
              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>First Name</div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="firstName"
                    maxLength={260}
                    onChange={handleSetFirstName}
                    value={firstName}
                    required
                  />
                </div>
              </div>

              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>Last Name</div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="lastName"
                    maxLength={260}
                    value={lastName}
                    onChange={handleSetLastName}
                    required
                  />
                </div>
              </div>
            </div>
          )}
          {/* names */}

          {selectedOption === "Person" && (
            <div className={styles.thirdformWrapper}>
              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>Date of Birth</div>
                <div className={styles.firstNameInput}>
                  <input
                    type="date"
                    id="date"
                    onChange={handlePersonDOB}
                    value={dob}
                    max={`${currentYear - 18}-01-01`} // Set the maximum allowed date 18 years ago
                    required
                  />
                </div>
              </div>

              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>Contact Email</div>
                <div className={styles.firstNameInput}>
                  <input
                    type="email"
                    id="contact_email"
                    value={email}
                    maxLength={260}
                    onChange={handlePersonEmail}
                    aria-required
                  />
                </div>
              </div>
            </div>
          )}
          {selectedOption === "Business" && (
            <div>
              <div className={styles.secondformWrapper}>
                <div className={styles.sec_firstName}>
                  <div className={styles.firstNameLabel}>Country name</div>
                  <div className={styles.firstNameInput}>
                    <select
                      name="citizenship"
                      value={citizenship}
                      onChange={(e) => setCitizenship(e.target.value)}
                      id="citizenship"
                      required
                    >
                      {" "}
                      <option value="-1">Please select a Country</option>
                      {countryTo.map((country) => (
                        <option key={country.id} value={country.country_name}>
                          {country.country_name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className={styles.sec_firstName}>
                  <div className={styles.firstNameLabel}>Contact Email</div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="email"
                      id="shareHolderemail"
                      maxLength={260}
                      value={shareHolderEmail}
                      onChange={handleCompanyEmail}
                      aria-required
                    />
                  </div>
                </div>
              </div>
              <div className={styles.secondformWrapper}>
                <div className={styles.sec_firstName}>
                  <div
                    htmlFor="shareHolderName"
                    className={styles.firstNameLabel}
                  >
                    UBO Shareholder Name
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="text"
                      maxLength={260}
                      id="shareHolderName"
                      value={shareHolderName}
                      onChange={handleShareHolderName}
                      required
                    />
                  </div>
                </div>

                <div className={styles.sec_firstName}>
                  <div className={styles.firstNameLabel}>
                    UBO Shareholder Date of Incorporation
                  </div>
                  <div className={styles.firstNameInput}>
                    <input
                      type="date"
                      id="date"
                      onChange={handleShareHolderDateOfIncorporation}
                      value={dateOfIncorporation}
                      // Set the maximum allowed date 18 years ago
                      required
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
          {selectedOption === "Business" || selectedOption === "Person" ? (
            <div className={styles.secondformWrapper}>
              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="currencyPayout">Currency of payout</label>
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    name="currencyPayout"
                    id="currencyPayout"
                    value={
                      currencyPayout
                        ? currencyPayout
                        : "Please select a country first"
                    }
                    readOnly
                    // onChange={handleCurrencyChangePayout}
                  />
                </div>
              </div>
              <div className={styles.sec_firstName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="currencyPayout">
                    Select Payment Method (TO)
                  </label>
                </div>
                <div className={styles.firstNameInput}>
                  <select
                    name="currencyPayout"
                    id="currencyPayout"
                    value={payMethodPayout}
                    onChange={handlePayMethodPayout}
                  >
                    <option value="-1">Please select a currency first</option>

                    {dataPayout !== undefined
                      ? dataPayout.map((PayMethodName) => (
                          <option key={PayMethodName.id}>
                            {PayMethodName.payment_method}
                          </option>
                        ))
                      : ""}
                  </select>
                </div>
              </div>
            </div>
          ) : (
            ""
          )}

          {totalFields.map((field, index) => (
            <div key={index} className={styles.secondformWrapper}>
              <div className={styles.sec_firstName1}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="upi/acc">{field.key}</label>
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="upi/acc"
                    maxLength={260}
                    placeholder={`Enter ${field.key}`}
                    onChange={(e) => {
                      const newUpiIds = accUpiIDs.filter(
                        (ids) => ids.key !== field.key
                      );
                      const value = e.target.value;
                      const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
                      const newUpiIds2 = [
                        ...newUpiIds,
                        { key: field.key, value: inputValue },
                      ];
                      setAccUpiIDs(newUpiIds2);
                    }}
                    value={
                      accUpiIDs.find((item) => item.key === field.key)
                        ? accUpiIDs.find((item) => item.key === field.key).value
                        : ""
                    }
                    required
                  />
                </div>
              </div>
            </div>
          ))}

          <div className={styles.listing_BtnCont}>
            <button
              className={styles.listing_Btn}
              onClick={submitRecipientHandler}
            >
              Add Recipient Account
            </button>
          </div>
        </div>
      </div>

    </>
  );
};

export default page;
