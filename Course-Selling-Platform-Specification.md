# Course Selling Platform - Complete Project Specification

## 🏗️ Tech Stack & Architecture

### Core Technologies
- **Framework**: Next.js 13+ with App Router
- **Frontend**: React 18+ with TypeScript support
- **Styling**: CSS Modules with modern design system
- **State Management**: React Context API for global state
- **Authentication**: JWT-based with 2FA support
- **Real-time**: WebSocket/SSE for live updates
- **UI Components**: Custom components with accessibility focus
- **Package Manager**: npm

### 📦 Key Dependencies

```json
{
  "dependencies": {
    "next": "^13.4.6",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.4.0",
    "react-toastify": "^9.1.3",
    "react-modal": "^3.16.1",
    "react-icons": "^5.5.0",
    "socket.io-client": "^4.7.5",
    "jwt-decode": "^4.0.0",
    "crypto-js": "^4.2.0",
    "dayjs": "^1.11.11",
    "lodash": "^4.17.21",
    "chart.js": "^2.9.3",
    "react-paginate": "^8.1.3",
    "react-form-stepper": "^2.0.3",
    "react-google-recaptcha": "^3.1.0",
    "react-player": "^2.12.0",
    "react-helmet": "^6.1.0"
  }
}
```

## 🎨 Design System & Visual Identity

### Color Palette
```css
/* Primary Brand Colors */
--primary-blue: #4153ED;
--primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

/* Success States */
--success-primary: #10b981;
--success-bg: #d1fae5;
--success-text: #059669;

/* Error States */
--error-primary: #ef4444;
--error-bg: #fee2e2;
--error-text: #dc2626;

/* Warning States */
--warning-primary: #f59e0b;
--warning-bg: #fef3c7;
--warning-text: #d97706;

/* Neutral Colors */
--background-primary: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
--text-primary: #1E293B;
--text-secondary: #64748B;
--border-color: #e2e8f0;
```

### Typography
```css
/* Primary Font */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

/* Font Weights */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### Component Styling Patterns
- **Cards**: White background, 16-20px border-radius, subtle shadows with hover effects
- **Buttons**: Gradient backgrounds, rounded corners, smooth transitions
- **Forms**: Clean input fields with focus states and validation styling
- **Modals**: Backdrop blur, centered positioning, smooth animations

## 📱 Responsive Design Requirements

### Mobile-First Approach
- **Breakpoints**: 576px (mobile), 768px (tablet), 1024px (desktop)
- **Mobile Navigation**: Hamburger menu with sidebar overlay
- **Card Layouts**: Stack vertically on mobile, grid on desktop
- **Typography**: Responsive font sizes with proper scaling
- **Touch Targets**: Minimum 44px for interactive elements

### Layout Patterns
```css
/* Desktop Layout */
.main {
  background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
  min-height: 100vh;
  display: flex;
  font-family: 'Poppins', sans-serif;
}

/* Mobile Responsive */
@media screen and (max-width: 576px) {
  .container {
    width: 98%;
    padding: 16px;
    margin: 0 auto;
  }
}
```

## 🏛️ Application Architecture

### Folder Structure
```
src/
├── app/
│   ├── components/          # Reusable UI components
│   │   ├── Layout/         # Main layout wrapper
│   │   ├── Sidebar/        # Navigation sidebar
│   │   ├── NotificationBox/ # Notification system
│   │   ├── LoadingSpinner/ # Loading states
│   │   ├── CourseCard/     # Course display cards
│   │   ├── VideoPlayer/    # Course video player
│   │   └── ...
│   ├── pages/              # Application pages
│   │   ├── dashboard/      # Student/Instructor dashboard
│   │   ├── courses/        # Course catalog & details
│   │   ├── my-courses/     # Enrolled courses
│   │   ├── create-course/  # Course creation (instructors)
│   │   ├── profile/        # User profile & settings
│   │   └── ...
│   ├── api/                # API integration layer
│   ├── context/            # React Context providers
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   └── lib/                # Third-party integrations
```

## 🔐 Authentication System

### Features
- **Login/Register**: Email-based with Google OAuth option
- **2FA Support**: TOTP-based two-factor authentication
- **Session Management**: JWT tokens with refresh mechanism
- **Password Security**: Secure password requirements
- **Captcha Integration**: reCAPTCHA for bot protection
- **Role-based Access**: Student, Instructor, Admin roles

### Implementation
```javascript
// Context providers for global state
<TimerContextProvider>
  <SSEProvider>
    <LayoutWrapper>{children}</LayoutWrapper>
  </SSEProvider>
</TimerContextProvider>

// Centralized API handling with token management
const customFetchWithToken = axios.create({
  baseURL: process.env.NEXT_PUBLIC_Base_URL,
  headers: {
    Authorization: `Bearer ${token}`
  }
});
```

## 📚 Core Features & Functionality

### 1. Dashboard (Student/Instructor)
- **Statistics Cards**: Enrolled courses, completed courses, learning progress
- **Progress Charts**: Learning analytics and completion rates
- **Recent Activity**: Course progress, new enrollments, notifications
- **Quick Actions**: Continue learning, browse courses, view certificates

### 2. Course Catalog
- **Course Listings**: Display courses with professional card design
- **Search & Filter**: By category, price, rating, difficulty level
- **Course Preview**: Video previews and detailed course information
- **Enrollment Process**: Modal-based enrollment flow

### 3. Course Player
- **Video Player**: Integrated video player with progress tracking
- **Course Navigation**: Lesson sidebar with progress indicators
- **Note Taking**: Student notes and bookmarks
- **Quiz Integration**: Interactive quizzes and assessments

### 4. Course Management (For Instructors)
- **Course Creation**: Step-by-step course creation wizard
- **Content Upload**: Video, documents, and resource management
- **Student Analytics**: Enrollment and progress tracking
- **Revenue Dashboard**: Earnings and sales analytics

### 5. User Profiles & Settings
- **Student Profile**: Learning history, certificates, preferences
- **Instructor Profile**: Course portfolio, ratings, bio
- **Account Settings**: Security settings with 2FA
- **Payment Methods**: For course purchases

### 6. Notification System
- **Real-time Notifications**: Course updates, new enrollments, messages
- **Notification Types**: Learning reminders, course announcements, system alerts
- **Visual Indicators**: Color-coded notification badges
- **Action Buttons**: Quick actions from notifications

## 🎯 Key Pages & Components

### Dashboard Component Example
```javascript
// Statistics cards with same styling as original
<div className={styles.statCard}>
  <div className={styles.statIcon}>📚</div>
  <div className={styles.statLabel}>Enrolled Courses</div>
  <div className={styles.statValue}>{enrolledCourses}</div>
</div>

<div className={styles.statCard}>
  <div className={styles.statIcon}>🎓</div>
  <div className={styles.statLabel}>Completed Courses</div>
  <div className={styles.statValue}>{completedCourses}</div>
</div>
```

### Course Card Component
```javascript
// Same card layout as original search results
<div className={styles.courseCard}>
  <div className={styles.courseImage}>
    <img src={course.thumbnail} alt={course.title} />
  </div>
  <div className={styles.courseInfo}>
    <h3 className={styles.courseTitle}>{course.title}</h3>
    <p className={styles.instructor}>{course.instructor}</p>
    <div className={styles.coursePrice}>${course.price}</div>
    <button className={styles.enrollBtn}>Enroll Now</button>
  </div>
</div>
```

### Course Player Component
```javascript
// New component with same styling patterns
<div className={styles.playerContainer}>
  <div className={styles.videoSection}>
    <ReactPlayer url={videoUrl} controls />
  </div>
  <div className={styles.courseNavigation}>
    <CourseStepper activeLesson={currentLesson} />
  </div>
</div>
```

## 🔧 Sidebar Navigation

### Student Sidebar
- Dashboard
- Browse Courses
- My Courses
- Certificates
- Profile
- Settings
- Logout

### Instructor Sidebar
- Dashboard
- My Courses
- Create Course
- Analytics
- Students
- Profile
- Settings
- Logout

## 💳 Payment Integration
- **Course Purchases**: Stripe/PayPal integration
- **Subscription Plans**: Monthly/yearly access plans
- **Refund System**: Course refund management
- **Revenue Tracking**: Instructor earnings dashboard

## 📊 Analytics & Progress Tracking
- **Learning Progress**: Video completion, quiz scores
- **Course Analytics**: Enrollment rates, completion rates
- **Student Engagement**: Time spent, interaction metrics
- **Instructor Insights**: Course performance, student feedback

## 🚀 Development Guidelines

### Component Architecture
- **Modular Design**: Reusable components with single responsibility
- **CSS Modules**: Scoped styling with consistent naming conventions
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Testing**: Unit tests for critical functionality
- **Documentation**: Inline code documentation and README files

### Performance & Security
- **Loading States**: Skeleton screens and spinners
- **Lazy Loading**: Component-based code splitting
- **Input Validation**: Client and server-side validation
- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: Token-based request validation

### Accessibility
- **WCAG 2.1 AA Compliance**: Full accessibility support
- **Keyboard Navigation**: Complete keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and roles
- **Color Contrast**: High contrast ratios for readability

## 🎨 Visual Consistency Guidelines

### Maintain Original Design Elements
- **Same gradient backgrounds and color schemes**
- **Same card designs and hover effects**
- **Same button styles and interactions**
- **Same modal designs and animations**
- **Same typography and spacing**
- **Same loading spinners and progress indicators**

### Component Styling Standards
```css
/* Card Component Standard */
.card {
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  padding: 24px;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* Button Component Standard */
.primaryButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primaryButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}
```

## 🛠️ Implementation Checklist

### Phase 1: Foundation Setup
- [ ] Initialize Next.js 13+ project with App Router
- [ ] Install all required dependencies
- [ ] Set up CSS Modules with design system variables
- [ ] Configure TypeScript (optional but recommended)
- [ ] Set up environment variables structure

### Phase 2: Authentication System
- [ ] Implement JWT-based authentication
- [ ] Create login/register pages with same styling
- [ ] Add Google OAuth integration
- [ ] Implement 2FA with TOTP
- [ ] Set up session management with refresh tokens
- [ ] Add reCAPTCHA integration

### Phase 3: Core Layout & Navigation
- [ ] Create Layout component with sidebar
- [ ] Implement responsive navigation
- [ ] Add notification system
- [ ] Create loading states and spinners
- [ ] Set up toast notifications

### Phase 4: Dashboard & Analytics
- [ ] Build student dashboard with stats cards
- [ ] Create instructor dashboard
- [ ] Implement progress tracking charts
- [ ] Add recent activity feeds

### Phase 5: Course Management
- [ ] Create course catalog with search/filter
- [ ] Build course detail pages
- [ ] Implement course player with video integration
- [ ] Add course creation wizard for instructors
- [ ] Create enrollment system

### Phase 6: User Management
- [ ] Build user profiles (student/instructor)
- [ ] Implement account settings
- [ ] Add payment method management
- [ ] Create certificate system

### Phase 7: Payment Integration
- [ ] Integrate Stripe/PayPal for payments
- [ ] Implement subscription plans
- [ ] Add refund system
- [ ] Create revenue tracking

### Phase 8: Real-time Features
- [ ] Set up WebSocket/SSE for notifications
- [ ] Implement real-time progress updates
- [ ] Add live course announcements

## 📋 API Endpoints Structure

### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/register
POST /api/auth/refresh-token
POST /api/auth/logout
POST /api/auth/verify-2fa
POST /api/auth/google-oauth
```

### Course Endpoints
```
GET /api/courses                    # Get all courses
GET /api/courses/:id               # Get course details
POST /api/courses                  # Create course (instructor)
PUT /api/courses/:id               # Update course
DELETE /api/courses/:id            # Delete course
POST /api/courses/:id/enroll       # Enroll in course
GET /api/courses/:id/lessons       # Get course lessons
```

### User Endpoints
```
GET /api/users/profile             # Get user profile
PUT /api/users/profile             # Update profile
GET /api/users/courses             # Get user's courses
GET /api/users/progress            # Get learning progress
POST /api/users/certificates       # Generate certificate
```

### Payment Endpoints
```
POST /api/payments/create-intent   # Create payment intent
POST /api/payments/confirm         # Confirm payment
GET /api/payments/history          # Payment history
POST /api/payments/refund          # Process refund
```

## 🎯 Database Schema Suggestions

### Users Table
```sql
users (
  id, email, password_hash, first_name, last_name,
  role (student/instructor/admin), is_verified,
  two_factor_enabled, created_at, updated_at
)
```

### Courses Table
```sql
courses (
  id, title, description, thumbnail, price,
  instructor_id, category, difficulty_level,
  is_published, created_at, updated_at
)
```

### Enrollments Table
```sql
enrollments (
  id, user_id, course_id, enrolled_at,
  progress_percentage, completed_at
)
```

### Lessons Table
```sql
lessons (
  id, course_id, title, video_url,
  duration, order_index, is_free_preview
)
```

## 🔧 Environment Variables

```env
# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Database
DATABASE_URL=your_database_url

# Authentication
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_refresh_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Payment
STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# File Storage
AWS_S3_BUCKET=your_s3_bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Email
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# reCAPTCHA
RECAPTCHA_SITE_KEY=your_recaptcha_site_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key
```

## 📱 Mobile App Considerations

### Progressive Web App (PWA)
- Add PWA manifest for mobile installation
- Implement offline course viewing
- Add push notifications for course updates
- Optimize for mobile performance

### Mobile-Specific Features
- Download courses for offline viewing
- Mobile-optimized video player
- Touch-friendly navigation
- Responsive video controls

## 🚀 Deployment & DevOps

### Recommended Hosting
- **Frontend**: Vercel or Netlify
- **Backend API**: Railway, Render, or AWS
- **Database**: PostgreSQL on Railway or AWS RDS
- **File Storage**: AWS S3 or Cloudinary
- **CDN**: CloudFlare for global content delivery

### CI/CD Pipeline
```yaml
# Example GitHub Actions workflow
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm install
      - run: npm run build
      - run: npm run test
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
```

---

This comprehensive specification provides everything needed to create a professional course selling platform that maintains the exact look, feel, and authentication structure of the original financial trading platform while being perfectly adapted for educational content delivery.
