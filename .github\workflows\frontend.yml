name: Frontend DEV Pipeline

on:
  push:
    branches:
      - copy-feature/chat

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: SSH to Server
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: ubuntu
          key: ${{ secrets.REMFLOW_PEM_KEY }}
          passphrase: ${{ secrets.REMFLOW_PASSWORD }}
          script: |
            echo "Logged in successfully"
            pwd
            bash start_remflow_frontend.sh
            # Add your server commands here

