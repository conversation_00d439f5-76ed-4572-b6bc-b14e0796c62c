import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const notifySellerApi = async (Data) => {
  const res = await axios({
    url: `${Base_url}/notify-seller/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};

export const notifyPeerApi = async (Data) => {
  const res = await axios({
    url: `${Base_url}/notify-buyer/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};
