.container {
    width: 100%;
    /* height: 200px; */
    padding: 30px 0px;
    /* background-color: aquamarine; */
    border-radius: 5px;
    border: 1px solid rgb(187, 183, 183);
    margin: 10px 0px;
}

.topBox {
    height: 70%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.bottomBox {
    height: 30%;
    background-color: #e3d7f5;
    /* background-color: #c8b6ff; */
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rate {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
    color: #50CD89;
}

.round {
    width: 30px;
    height: 30px;
    background-color: #EC8413;
    border-radius: 50%;


}

.status {
    /* width: 100px; */
    /* color: rgb(209, 179, 7); */
    margin-bottom: 3px;
    font-size: 12px;
    font-weight: 600;
    font-style: italic;
    /* background: #EECDA3; */
    /* fallback for old browsers */
    background: -webkit-linear-gradient(to right, #EF629F, #EECDA3);
    /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to right, #EF629F, #EECDA3);
    /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

/* nameCont */

.nameCont {
    padding: 0px 15px
}

.imageCont {
    margin-right: 5px;
}

.image {
    border: 1px solid red;
    border-radius: 50%;
    /* border: 1px solid #333131; */
}

.name {
    font-size: 14px;
    font-weight: 400;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.seen {
    font-size: 12px;
    font-weight: 400;
    font-family: poppins;
}

.icons {
    margin: 0px 3px;
}

.seenSymbol {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #197D47;
}

/* feedbackcont */
.feedbackCont {
    font-size: 12px;
    padding: 0px 15px
}

.completion {
    margin-bottom: 2px;
}

.orders {
    margin-bottom: 2px;
    font-weight: 600;

}

.thumbCont {
    display: flex;
}

.thumbsUp {
    margin-bottom: 2px;
}

.thumbDown {
    margin-bottom: 2px;
    margin-left: 3px;
}

/* currencyCont */
.currencyCont {
    padding: 0px 15px
}

.currencyModes {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 2px;
}

.payinCurrency {
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 2px;
}

.payoutCurrency {
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 2px;
}

.values {
    font-size: 12px;
    color: gray;

}

.blueDash {
    height: 10px;
    width: 3px;
    border-radius: 5px;
    background-color: #333131;
    color: #333131;

}

/* liquidityCont */
.liquidityCont {
    padding: 0px 15px;
}

.availableLiquidity {
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 2px;
}

.minimumLiquidity {
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 2px;
}

.maximumLiquidity {
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 2px;
}

/* buyBtnContainer */

.buyBtnCont {
    margin-left: auto;
    margin-right: 30px;
    background-color: #b2eecf;
    border-radius: 5px;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;

}

.buy {
    color: #50CD89;
    padding: 10px 20px;
    background-color: transparent;
    border: none;
    font-weight: 600;
    font-family: poppins;
    cursor: pointer;
}

/* modal */

.modalCont {
    display: flex;
    width: 700px;
    padding-bottom: 0px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 30px;
}

.modalTop {
    width: 100%;
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #858585;
}

.left {
    width: 85%;
    height: 63px;
}


.right {
    width: 15%;
    /* background-color: #50CD89; */
    display: flex;
    justify-content: flex-end;
    /* align-items: center; */
}

.modalProfileWrapper {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    align-items: center;


}

.orders {

    color: #000;
    font-family: Poppins;
    font-size: 10px;

    font-weight: 600;

    margin-right: 10px;

}


.modalName {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 11px;

}

.modalBottom {
    width: 100%;
    display: flex;

}

.leftB {
    width: 50%;
    display: flex;
    margin-right: auto;
    flex-direction: column;

}

.tHeader {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 11px;

    text-decoration-line: underline;
}

.termsPara {
    color: #363636;
    font-family: Poppins;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;

}

.info {
    border: 1px solid #EBEBEB;
    background: #FFF;
    display: inline-flex;
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.infoPoints {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;

}

.highlight {

    font-weight: 500;
}

.terms {
    margin-top: 30px;
}

.tHeader {
    margin-bottom: 20px;
}

.rightB {
    width: 40%;
}

.payInput {
    display: flex;
    height: 40px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    background: #F9F9F9;

}

.paySelect {
    width: 70px;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    height: 100%;
    background-color: transparent;
    border: 1px solid #BABABA;
}

.payInput input {
    outline: none;
    border: none;
    height: 100%;
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    background-color: transparent;
}

.receive {
    outline: none;
    border: none;
    /* height: 100%; */
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    background-color: transparent;
}

.buttonBBUY {
    margin-top: 20px;
    display: flex;
    /* width: 93%; */
    height: 40px;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #E8FFF3;
    color: #50CD89;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    cursor: pointer;
    /* 91.667% */
}


/* modal */

.thead {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.resultsSection {
    display: flex;
    flex-direction: column;
    padding: 30px 0px;
    gap: 20px;
    border-radius: 20px;
    background: #fff;
    display: flex;
    width: 100%;
    /* justify-content: space-around; */
    /* align-items: center; */
}

.resultsSectionCont {
    display: flex;
    width: 95%;
    height: auto;
    padding: 10px 0px;
    flex-direction: column;

    @media screen and (max-width : 576px) {
        display: none;

    }

}

/* .tabBodyyCont {
    width: 100%;
} */

.tabBodyy {
    /* width: 100%;
    display: inline-table */
}

.tradeDiv {
    font-size: 12px;
}

.names {
    font-size: 16px;
}

.validationCheck {
    color: red;
    font-size: 12px;
    font-family: poppins;
    font-weight: 600;
}