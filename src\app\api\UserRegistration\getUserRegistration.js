import axios from "axios";

const Base_URL = process.env.NEXT_PUBLIC_Base_URL;

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}
const getUserRegistrationApi = async () => {
  const res = await axios({
    url: `${Base_URL}/view-user-details/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return res;
};

module.exports = getUserRegistrationApi;
