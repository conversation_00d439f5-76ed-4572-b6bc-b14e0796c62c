.container {
  display: flex;
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease-out;
  border-left: 4px solid #6c5ce7; /* Accent color */
}

.container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.icon {
  flex-shrink: 0;
  margin-right: 16px;
  color: #6c5ce7; /* Accent color */
}

.content {
  flex-grow: 1;
  min-width: 0;
}

.message {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  overflow-wrap: break-word;
}

.date {
  font-size: 12px;
  color: #888;
  margin-bottom: 12px;
}

.actions {
  display: flex;
  gap: 10px;
}

.button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #fff;
}

.accept {
  background-color: #28a745; /* Green */
}

.accept:hover {
  background-color: #218838;
  transform: translateY(-1px);
}

.reject {
  background-color: #dc3545; /* Red */
}

.reject:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

.closeButton {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 12px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.closeButton:hover {
  opacity: 1;
}

.remove {
  opacity: 0;
  transform: scale(0.95);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 0;
  border-width: 0;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
}
