import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

/**
 * Fetch transaction overview data for charts
 * @param {string} period - Time period: 'weekly', 'monthly', or 'yearly'
 * @returns {Promise<Object>} API response with transaction data
 */
export const getTransactionOverview = async (period = 'weekly') => {
  try {
    const response = await customFetchWithToken.get(`/analytics/transaction-overview/?period=${period}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching transaction overview:', error);
    throw error;
  }
};

/**
 * Expected API Response Structure:
 * {
 *   "success": true,
 *   "data": {
 *     "transactions": [7, 9, 22, 25, 7, 10, 13],
 *     "labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
 *     "currency": "USD",
 *     "time": "monthly"
 *   }
 * }
 */ 