/* Global styles for PersonalDetails page */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Fix for mobile viewport width issues - only hide horizontal scroll */
html,
body {
    overflow-x: hidden;
    overflow-y: visible;
    max-width: 100%;
    height: auto;
    min-height: 100%;
}

/* Add any other global styles needed for this component */
input,
select,
button {
    box-sizing: border-box;
}