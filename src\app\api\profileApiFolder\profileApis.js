import axios from "axios";

const Base_Url = process.env.NEXT_PUBLIC_Base_URL;

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const updatePassApi = async (Data) => {
  const res = await axios({
    url: `${Base_Url}/update-user-password/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};
export const getUserProfileInfo = async () => {
  const res = await axios({
    url: `${Base_Url}/view-user-details/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};

export const profileInfoChangeApi = async (Data) => {
  const res = await axios({
    url: `${Base_Url}/edit-user-details/`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "multipart/form-data",
    },
    data: Data,
  });
  return res;
};

export const deleteProfileApi = async () => {
  const res = await axios({
    url: `${Base_Url}/edit-user-details/`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: {
      flag: "delete",
    },
  });
  return res;
};
export const silverStatusProfileApi = async (Data) => {
  const res = await axios({
    url: `${Base_Url}/upload-document/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};
