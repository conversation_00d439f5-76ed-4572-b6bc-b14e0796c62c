import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const getTradeTime = async (orderNo) => {
  const res = await axios({
    url: `${Base_url}/trade-request-clock/?order_number=${orderNo}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const increaseTradeTime = async (confirmation, orderNumber) => {
  const res = await axios({
    url: `${Base_url}/trade-increase-time/?flag=${confirmation}&order_number=${orderNumber}`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
