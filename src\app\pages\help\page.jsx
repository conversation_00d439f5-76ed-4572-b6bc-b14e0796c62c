"use client";
import { useState, useEffect } from "react";
import styles from "./help.module.css";
import Layout from "../../components/Layout/page";
import SupportTicketModal from "../../components/SupportTicketModal/page";
import { customFetchWithToken } from "../../utils/axiosInterpreter";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const Page = () => {
  const [activeAccordion, setActiveAccordion] = useState(null);
  const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);
  const [supportTickets, setSupportTickets] = useState([]);
  const [isLoadingTickets, setIsLoadingTickets] = useState(true);
  const [activeTicket, setActiveTicket] = useState(null);
  const [isTicketsSectionOpen, setIsTicketsSectionOpen] = useState(false);

  const toggleAccordion = (index) => {
    setActiveAccordion(activeAccordion === index ? null : index);
  };

  const toggleTicket = (index) => {
    setActiveTicket(activeTicket === index ? null : index);
  };

  const toggleTicketsSection = () => {
    setIsTicketsSectionOpen(!isTicketsSectionOpen);
  };

  const fetchSupportTickets = async () => {
    try {
      setIsLoadingTickets(true);
      const response = await customFetchWithToken.get("/get-ticket-by-id/");
      
      if (response.data && response.data.data) {
        setSupportTickets(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching support tickets:", error);
      if (error.response?.status !== 401) {
        toast.error("Failed to load support tickets");
      }
    } finally {
      setIsLoadingTickets(false);
    }
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  const getTicketStatus = (ticket) => {
    if (ticket.answer) {
      return { status: 'Resolved', class: 'resolved' };
    }
    return { status: 'Open', class: 'open' };
  };

  useEffect(() => {
    fetchSupportTickets();
  }, []);

  // Refresh tickets when modal closes (in case a new ticket was created)
  const handleModalClose = () => {
    setIsSupportModalOpen(false);
    fetchSupportTickets();
  };

  const faqData = [
    {
      question: "How do I create a trade request?",
      answer: "To create a trade request, navigate to the Search page, find a suitable listing, enter your desired amount, and click 'Send Trade Request'. You'll need to have a recipient account set up before initiating the trade."
    },
    {
      question: "What payment methods are supported?",
      answer: "Remflow supports various payment methods including bank transfers, digital wallets, and cryptocurrency. You can add and manage your payment methods in the 'Saved Payment Methods' section under Accounts."
    },
    {
      question: "How do I add a recipient account?",
      answer: "Go to Accounts > Add Accounts > Add Recipient Accounts. Fill in the required details including recipient name, email, country, and payout method. Make sure all information is accurate to avoid delays."
    },
    {
      question: "What should I do if my trade is disputed?",
      answer: "If you encounter issues with a trade, you can file a dispute by going to the Dispute section. Provide detailed information, upload evidence, and our support team will review your case within 24-48 hours."
    },
    {
      question: "How do I deposit funds to my Remflow wallet?",
      answer: "Navigate to Remflow Funds, select your preferred cryptocurrency network (ETH or TRX), and use the provided wallet address to deposit USDT. Funds typically reflect within 10-30 minutes depending on network congestion."
    },
    {
      question: "What are the trading fees?",
      answer: "Trading fees vary by listing and are set by individual traders. You can see the exact fee before confirming any trade. Fees typically range from 0.5% to 5% depending on the currency pair and payment method."
    },
    {
      question: "How do I enable two-factor authentication (2FA)?",
      answer: "Go to your Profile settings, scroll to the Security section, and click 'Generate QR Code'. Scan the QR code with your authenticator app and enter the verification code to enable 2FA for enhanced account security."
    },
    {
      question: "What happens if I don't complete a trade on time?",
      answer: "Each trade has a specific time duration set by the trader. If you don't complete the trade within this timeframe, it may be automatically cancelled. Always check the timer and complete your part of the trade promptly."
    },
    {
      question: "How do I contact customer support?",
      answer: "You can contact our support team through the Help Desk section. Create a support ticket with your query, attach any relevant evidence, and our team will respond within 24 hours. For urgent issues, include 'URGENT' in your ticket title."
    },
    {
      question: "Can I cancel a trade request?",
      answer: "Yes, you can cancel a trade request before it's accepted by the other party. Once a trade is accepted and in progress, cancellation requires mutual agreement or dispute resolution through our support team."
    }
  ];

  const helpTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Help Center</h1>
      <p className={styles.pageSubtitle}>
        Find answers to commonly asked questions
      </p>
    </div>
  );

  return (
    <>
      <div style={{ overflowX: "hidden", width: "100%" }}>
        <Layout title={helpTitle}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.mobileHeader}>
            <div className={styles.headerContent}>
              <h1 className={styles.pageTitle}>Help Center</h1>
              <p className={styles.pageSubtitle}>
                Find answers to commonly asked questions
              </p>
            </div>
          </div>

          <div className={styles.rightContainerBody}>
            <div className={styles.helpContainer}>
              <div className={styles.welcomeSection}>
                <div className={styles.welcomeIcon}>❓</div>
                <h2 className={styles.welcomeTitle}>How can we help you?</h2>
                <p className={styles.welcomeDescription}>
                  Browse through our frequently asked questions to find quick answers to common queries about Remflow's services.
                </p>
              </div>

              <div className={styles.faqSection}>
                <h3 className={styles.faqTitle}>
                  <span className={styles.faqIcon}>📋</span>
                  Frequently Asked Questions
                </h3>

                <div className={styles.accordionContainer}>
                  {faqData.map((faq, index) => (
                    <div key={index} className={styles.accordionItem}>
                      <button
                        className={`${styles.accordionHeader} ${
                          activeAccordion === index ? styles.accordionHeaderActive : ''
                        }`}
                        onClick={() => toggleAccordion(index)}
                        aria-expanded={activeAccordion === index}
                        aria-controls={`accordion-content-${index}`}
                      >
                        <span className={styles.accordionQuestion}>{faq.question}</span>
                        <span className={`${styles.accordionIcon} ${
                          activeAccordion === index ? styles.accordionIconRotated : ''
                        }`}>
                          ▼
                        </span>
                      </button>

                      <div
                        id={`accordion-content-${index}`}
                        className={`${styles.accordionContent} ${
                          activeAccordion === index ? styles.accordionContentActive : ''
                        }`}
                        role="region"
                        aria-labelledby={`accordion-header-${index}`}
                      >
                        <div className={styles.accordionAnswer}>
                          {faq.answer}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* My Support Tickets Section */}
              <div className={styles.ticketsSection}>
                <button
                  className={`${styles.ticketsTitle} ${styles.ticketsTitleButton} ${
                    isTicketsSectionOpen ? styles.ticketsTitleActive : ''
                  }`}
                  onClick={toggleTicketsSection}
                  aria-expanded={isTicketsSectionOpen}
                  aria-controls="tickets-content"
                >
                  <div className={styles.ticketsTitleLeft}>
                    <span className={styles.ticketsIcon}>🎫</span>
                    My Support Tickets
                    {supportTickets.length > 0 && (
                      <span className={styles.ticketCount}>{supportTickets.length}</span>
                    )}
                  </div>
                  <span className={`${styles.ticketsToggleIcon} ${
                    isTicketsSectionOpen ? styles.ticketsToggleIconRotated : ''
                  }`}>
                    ▼
                  </span>
                </button>

                <div 
                  id="tickets-content"
                  className={`${styles.ticketsContainer} ${
                    isTicketsSectionOpen ? styles.ticketsContainerOpen : styles.ticketsContainerClosed
                  }`}
                >
                  {isLoadingTickets ? (
                    <div className={styles.loadingState}>
                      <div className={styles.loadingSpinner}></div>
                      <p>Loading your support tickets...</p>
                    </div>
                  ) : supportTickets.length === 0 ? (
                    <div className={styles.emptyState}>
                      <div className={styles.emptyIcon}>📝</div>
                      <h4>No Support Tickets</h4>
                      <p>You haven't created any support tickets yet. Click below to create your first ticket.</p>
                    </div>
                  ) : (
                    <div className={styles.ticketsAccordion}>
                      {supportTickets.map((ticket, index) => {
                        const { status, class: statusClass } = getTicketStatus(ticket);
                        return (
                          <div key={ticket.id} className={styles.ticketItem}>
                            <button
                              className={`${styles.ticketHeader} ${
                                activeTicket === index ? styles.ticketHeaderActive : ''
                              }`}
                              onClick={() => toggleTicket(index)}
                              aria-expanded={activeTicket === index}
                              aria-controls={`ticket-content-${index}`}
                            >
                              <div className={styles.ticketHeaderLeft}>
                                <div className={styles.ticketSubject}>{ticket.subject}</div>
                                <div className={styles.ticketMeta}>
                                  <span className={`${styles.ticketStatus} ${styles[statusClass]}`}>
                                    {status}
                                  </span>
                                  <span className={styles.ticketDate}>
                                    {formatDate(ticket.created_date)}
                                  </span>
                                </div>
                              </div>
                              <span className={`${styles.ticketIcon} ${
                                activeTicket === index ? styles.ticketIconRotated : ''
                              }`}>
                                ▼
                              </span>
                            </button>

                            <div
                              id={`ticket-content-${index}`}
                              className={`${styles.ticketContent} ${
                                activeTicket === index ? styles.ticketContentActive : ''
                              }`}
                              role="region"
                              aria-labelledby={`ticket-header-${index}`}
                            >
                              <div className={styles.ticketBody}>
                                <div className={styles.ticketBodySection}>
                                  <h5>Your Message:</h5>
                                  <p>{ticket.body}</p>
                                </div>
                                {ticket.answer && (
                                  <div className={styles.ticketResponseSection}>
                                    <h5>Support Response:</h5>
                                    <p>{ticket.answer}</p>
                                  </div>
                                )}
                                <div className={styles.ticketDetails}>
                                  <small>
                                    Created: {formatDate(ticket.created_date)}
                                    {ticket.updated_date !== ticket.created_date && (
                                      <> • Updated: {formatDate(ticket.updated_date)}</>
                                    )}
                                  </small>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>

              <div className={styles.contactSection}>
                <div className={styles.contactCard}>
                  <div className={styles.contactIcon}>💬</div>
                  <h4 className={styles.contactTitle}>Still need help?</h4>
                  <p className={styles.contactDescription}>
                    Can't find what you're looking for? Our support team is here to help.
                  </p>
                  <button 
                    className={styles.contactButton}
                    onClick={() => setIsSupportModalOpen(true)}
                  >
                    <span className={styles.buttonIcon}>🎫</span>
                    Create Support Ticket
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Layout>
      </div>

      {/* Support Ticket Modal */}
      <SupportTicketModal
        isOpen={isSupportModalOpen}
        onClose={handleModalClose}
      />

      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </>
  );
};

export default Page;
