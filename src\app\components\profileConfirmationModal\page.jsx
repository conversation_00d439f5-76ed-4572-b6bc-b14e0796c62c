'use client';
import { useState } from "react";
import Modal from "react-modal";
import styles from "./profileConfirmationModal.module.css";

const ConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
  confirmButtonText = "Confirm",
  cancelButtonText = "Cancel"
}) => {
  const customStyles = {
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      zIndex: 999999,
      position: "fixed",
    },
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      padding: "20px",
      borderRadius: "8px",
      width: "400px",
      textAlign: "center",
      zIndex: 999999,
      position: "fixed",
    },
  };

  return (
    <div>
      <Modal
        isOpen={isOpen}
        onRequestClose={onClose}
        style={customStyles}
        contentLabel="Confirmation Modal"
        ariaHideApp={false}
        parentSelector={() => document.body}
      >
        <div className={styles.modalContent}>
          <h2>{title}</h2>
          <p>{message}</p>
          <div className={styles.modalActions}>
            <button className={styles.cancelButton} onClick={onClose}>
              {cancelButtonText}
            </button>
            <button className={styles.confirmButton} onClick={onConfirm}>
              {confirmButtonText}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ConfirmationModal;
