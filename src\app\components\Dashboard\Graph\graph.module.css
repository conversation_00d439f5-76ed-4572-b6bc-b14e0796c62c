/* Modern Transaction Graph Styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Main Graph Wrapper */
.graphWrapper {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #FFFFFF;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

}

.graphWrapper:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* Rainbow Gradient Border */
.graphWrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3B82F6, #10B981, #F59E0B, #EF4444, #8B5CF6);
  opacity: 1;
  z-index: 1;
}

/* Header Section */
.graphHeader {
  padding: 28px 32px 20px;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
  z-index: 2;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.titleSection {
  flex: 1;
}

.graphTitle {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 6px 0;
  letter-spacing: -0.5px;
}

.graphSubtitle {
  font-size: 14px;
  color: #64748B;
  margin: 0;
  font-weight: 500;
}

/* Controls Section */
.controlsSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.timeRangeSelector {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.8);
  padding: 12px 16px;
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.timeRangeSelector:hover {
  border-color: #3B82F6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.selectorLabel {
  font-size: 13px;
  font-weight: 600;
  color: #475569;
  margin: 0;
  white-space: nowrap;
}

.modernSelect {
  background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFC 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 10px;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
  color: #1E293B;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
  min-width: 120px;
}

.modernSelect:hover {
  border-color: #3B82F6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.modernSelect:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modernSelect:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refreshButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 12px;
}

.refreshButton:hover {
  background: linear-gradient(135deg, #E2E8F0 0%, #CBD5E1 100%);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.refreshButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.refreshButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.refreshIcon {
  font-size: 18px;
  color: #64748B;
  transition: transform 0.3s ease;
}

.refreshButton:hover .refreshIcon {
  color: #3B82F6;
  transform: rotate(180deg);
}

/* Chart Container */
.chartContainer {
  position: relative;
  background: #FFFFFF;
}

.chartWrapper {
  position: relative;
  padding: 32px;
  height: 400px;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.chartCanvas {
  width: 100% !important;
  height: 100% !important;
  transition: opacity 0.3s ease;
}

/* Loading States */
.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  z-index: 10;
  transition: opacity 0.3s ease;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top: 3px solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-size: 14px;
  font-weight: 500;
  color: #64748B;
}

/* Chart Footer */
.chartFooter {
  padding: 20px 32px 28px;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.chartInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.currentPeriod {
  font-size: 14px;
  font-weight: 600;
  color: #1E293B;
}

.dataSource {
  font-size: 12px;
  color: #64748B;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.dataSource::before {
  content: '';
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #10B981, #059669);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.cacheIndicator {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.8;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .graphWrapper {
    border-radius: 20px;
    margin: 15px 0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .graphHeader {
    padding: 20px 20px 16px;
    text-align: center;
  }

  .headerContent {
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;
  }

  .titleSection {
    text-align: center;
    width: 100%;
  }

  .graphTitle {
    font-size: 20px;
    text-align: center;
  }

  .graphSubtitle {
    font-size: 13px;
    text-align: center;
  }

  .controlsSection {
    justify-content: center;
    width: 100%;
    display: flex;
    align-items: center;
  }

  .timeRangeSelector {
    padding: 10px 14px;
    border-radius: 12px;
    justify-content: center;
    margin: 0 auto;
  }

  .refreshButton {
    width: 36px;
    height: 36px;
    margin-left: 8px;
  }

  .refreshIcon {
    font-size: 16px;
  }

  .selectorLabel {
    font-size: 12px;
    text-align: center;
  }

  .modernSelect {
    font-size: 12px;
    padding: 6px 10px;
    min-width: 100px;
    text-align: center;
  }

  .chartWrapper {
    padding: 16px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
  }

  .chartFooter {
    padding: 16px 20px 20px;
    text-align: center;
  }

  .chartInfo {
    flex-direction: column;
    gap: 8px;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .currentPeriod {
    font-size: 13px;
    text-align: center;
  }

  .dataSource {
    font-size: 11px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .graphWrapper {
    border-radius: 16px;
    margin: 0 4px;
  }

  .graphHeader {
    padding: 16px 16px 12px;
  }

  .graphTitle {
    font-size: 18px;
  }

  .chartWrapper {
    padding: 12px;
    height: 250px;
    margin: 0;
  }

  .timeRangeSelector {
    width: 100%;
    justify-content: space-between;
  }

  .modernSelect {
    flex: 1;
    max-width: 140px;
  }
}

/* Enhanced Hover Effects */
.modernSelect option {
  background: #FFFFFF;
  color: #1E293B;
  padding: 8px;
}

/* Focus States for Accessibility */
.modernSelect:focus-visible {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  .graphWrapper {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(71, 85, 105, 0.3);
  }

  .graphHeader,
  .chartFooter {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
    border-color: rgba(71, 85, 105, 0.3);
  }

  .graphTitle {
    background: linear-gradient(135deg, #F1F5F9 0%, #E2E8F0 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .graphSubtitle,
  .selectorLabel,
  .loadingText {
    color: #94A3B8;
  }

  .currentPeriod {
    color: #F1F5F9;
  }

  .dataSource {
    color: #94A3B8;
  }

  .timeRangeSelector {
    background: rgba(51, 65, 85, 0.8);
    border-color: rgba(71, 85, 105, 0.5);
  }

  .modernSelect {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
    border-color: rgba(71, 85, 105, 0.5);
    color: #F1F5F9;
  }

  .loadingOverlay {
    background: rgba(30, 41, 59, 0.95);
  }
}

/* Animation Enhancements */
.graphWrapper {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Subtle Border Animation */
.graphWrapper::before {
  animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background: linear-gradient(90deg, #3B82F6, #10B981, #F59E0B, #EF4444, #8B5CF6);
  }
  25% {
    background: linear-gradient(90deg, #10B981, #F59E0B, #EF4444, #8B5CF6, #3B82F6);
  }
  50% {
    background: linear-gradient(90deg, #F59E0B, #EF4444, #8B5CF6, #3B82F6, #10B981);
  }
  75% {
    background: linear-gradient(90deg, #EF4444, #8B5CF6, #3B82F6, #10B981, #F59E0B);
  }
}

.errorMessage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #FEE2E2;
  color: #DC2626;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}