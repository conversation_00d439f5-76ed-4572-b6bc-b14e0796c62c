/* Container Styles */
.spinnerContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: auto;
    padding: 40px 20px;
    width: 100%;
    height: 100%;
    min-height: 200px;
}

/* Spinner Wrapper with Si<PERSON> Variants */
.spinnerWrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinnerWrapper.small {
    width: 60px;
    height: 60px;
}

.spinnerWrapper.medium {
    width: 80px;
    height: 80px;
}

.spinnerWrapper.large {
    width: 100px;
    height: 100px;
}

/* Outer Ring - Slow rotation with gradient */
.outerRing {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid transparent;
    background: linear-gradient(45deg, #1e40af, #3b82f6, #06b6d4, #10b981) border-box;
    background-clip: padding-box, border-box;
    animation: rotateClockwise 3s linear infinite;
    opacity: 0.8;
}

.outerRing::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, #1e40af, #3b82f6, #06b6d4, #10b981);
    z-index: -1;
    animation: gradientShift 2s ease-in-out infinite alternate;
}

/* Middle Ring - Pulsing effect */
.middleRing {
    position: absolute;
    width: 75%;
    height: 75%;
    border-radius: 50%;
    border: 2px solid rgba(59, 130, 246, 0.4);
    animation: pulse 2s ease-in-out infinite;
}

/* Inner Dots - Counter-rotating */
.innerDots {
    position: absolute;
    width: 50%;
    height: 50%;
    animation: rotateCounterClockwise 2s linear infinite;
}

.dot {
    position: absolute;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.6);
}

.dot:nth-child(1) {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.dot:nth-child(2) {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: 0.33s;
}

.dot:nth-child(3) {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0.66s;
}

/* Central Financial Icon */
.centerIcon {
    position: absolute;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: iconPulse 2s ease-in-out infinite;
}

.iconSvg {
    width: 100%;
    height: 100%;
    fill: #1e40af;
    filter: drop-shadow(0 0 8px rgba(30, 64, 175, 0.4));
}

/* Text Container */
.textContainer {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.spinnerText {
    color: #1e40af;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    margin: 0;
    letter-spacing: 0.025em;
    animation: textFade 2s ease-in-out infinite;
}

/* Progress Dots */
.progressDots {
    display: flex;
    gap: 6px;
    align-items: center;
}

.progressDot {
    width: 6px;
    height: 6px;
    background: #3b82f6;
    border-radius: 50%;
    animation: dotBounce 1.4s ease-in-out infinite both;
}

.progressDot:nth-child(1) {
    animation-delay: -0.32s;
}

.progressDot:nth-child(2) {
    animation-delay: -0.16s;
}

.progressDot:nth-child(3) {
    animation-delay: 0s;
}

/* Keyframe Animations */
@keyframes rotateClockwise {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotateCounterClockwise {
    from {
        transform: rotate(360deg);
    }
    to {
        transform: rotate(0deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes gradientShift {
    0% {
        filter: hue-rotate(0deg);
    }
    100% {
        filter: hue-rotate(20deg);
    }
}

@keyframes textFade {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

@keyframes dotBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .spinnerContainer {
        padding: 30px 15px;
        min-height: 150px;
    }

    .spinnerWrapper.small {
        width: 50px;
        height: 50px;
    }

    .spinnerWrapper.medium {
        width: 70px;
        height: 70px;
    }

    .spinnerWrapper.large {
        width: 90px;
        height: 90px;
    }

    .spinnerText {
        font-size: 14px;
    }

    .centerIcon {
        width: 20px;
        height: 20px;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .outerRing,
    .innerDots,
    .dot,
    .centerIcon {
        animation-duration: 3s;
        animation-timing-function: ease-in-out;
    }

    .middleRing {
        animation: none;
        opacity: 0.6;
    }

    .progressDot {
        animation: none;
        opacity: 0.8;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .spinnerText {
        color: #60a5fa;
    }

    .outerRing::before {
        background: linear-gradient(45deg, #3b82f6, #60a5fa, #06b6d4, #10b981);
    }

    .dot {
        background: linear-gradient(135deg, #60a5fa, #3b82f6);
        box-shadow: 0 0 10px rgba(96, 165, 250, 0.6);
    }

    .iconSvg {
        fill: #60a5fa;
        filter: drop-shadow(0 0 8px rgba(96, 165, 250, 0.4));
    }

    .progressDot {
        background: #60a5fa;
    }
}