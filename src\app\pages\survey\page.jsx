"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function USDTQuestionnaire() {
  const router = useRouter();
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [responses, setResponses] = useState({
    buyOrSell: "",
    country: "",
    localCurrency: "",
    tradingVolume: "",
  });
  const [phoneCountryArray, setPhoneCountryArray] = useState([]);
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const getPhoneCountryDropDown = async () => {
    try {
      const res = await customFetchWithToken.get("/country-code/");
      setPhoneCountryArray(res.data.data);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchAllCurrency = async () => {
    try {
      const resCurrency = await fetch(`${BaseURL}/currency-list/`);
      const data = await resCurrency.json();
      setLoadCurrencyFrom(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setResponses((prev) => ({ ...prev, [name]: value }));
  };

  const Data = {
    buy_or_sell: responses.buyOrSell,
    country: responses.country,
    currency: responses.localCurrency,
    trading_volume: responses.tradingVolume,
  };

  // Client-side validation
  const validateForm = () => {
    if (!responses.buyOrSell) {
      toast.error("Please select if you are looking to buy or sell USDT.");
      return false;
    }
    if (!responses.localCurrency || responses.localCurrency === "-1") {
      toast.error("Please select a local currency.");
      return false;
    }
    if (!responses.tradingVolume || responses.tradingVolume <= 0) {
      toast.error("Please enter a valid trading volume greater than 0.");
      return false;
    }
    if (!responses.country) {
      toast.error("Please enter your country.");
      return false;
    }
    return true;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Run client-side validation
    if (!validateForm()) {
      return; // Stop submission if validation fails
    }

    try {
      const res = await customFetchWithToken.post("/transaction-view/", Data);

      if (res.status === 200) {
        toast.success("Response submitted successfully");
      }
      setTimeout(function () {
        router.push("/pages/searchads");
      }, 1500);
    } catch (error) {
      console.error(error);
      toast.error("Error in submitting response");
    }
  };

  useEffect(() => {
    fetchAllCurrency();
  }, []);

  useEffect(() => {
    getPhoneCountryDropDown();
  }, []);

  const styles = {
    container: {
      fontFamily: "Arial, sans-serif",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minHeight: "100vh",
      backgroundColor: "#f0f0f0",
    },
    modalContent: {
      backgroundColor: "white",
      padding: "20px",
      borderRadius: "5px",
      width: "90%",
      maxWidth: "500px",
      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    },
    modalTitle: {
      marginTop: "0",
      marginBottom: "20px",
      fontSize: "24px",
      color: "#333",
      textAlign: "center",
    },
    questionGroup: {
      marginBottom: "20px",
    },
    label: {
      display: "block",
      marginBottom: "5px",
      fontWeight: "bold",
      color: "#555",
    },
    input: {
      width: "100%",
      paddingTop: "10px",
      paddingBottom: "10px",
      paddingLeft: "0px",
      paddingRight: "0px",
      border: "1px solid #ccc",
      borderRadius: "4px",
      fontSize: "16px",
    },
    submitButton: {
      width: "100%",
      padding: "10px",
      backgroundColor: "#0070f3",
      color: "white",
      border: "none",
      borderRadius: "4px",
      cursor: "pointer",
      fontSize: "16px",
      transition: "background-color 0.3s ease",
    },
  };

  return (
    <div>
      <div style={styles.container}>
        <div style={styles.modalContent}>
          <h2 style={styles.modalTitle}>Questionnaire</h2>
          <form onSubmit={handleSubmit}>
            <div style={styles.questionGroup}>
              <label style={styles.label} htmlFor="buyOrSell">
                Are you looking to buy or sell USDT?
              </label>
              <select
                id="buyOrSell"
                name="buyOrSell"
                value={responses.buyOrSell}
                onChange={handleInputChange}
                required
                style={styles.input}
              >
                <option value="">Select an option</option>
                <option value="buy">Buy</option>
                <option value="sell">Sell</option>
              </select>
            </div>

            <div style={styles.questionGroup}>
              <label style={styles.label} htmlFor="localCurrency">
                What local currency do you have?
              </label>
              <select
                id="localCurrency"
                name="localCurrency"
                value={responses.localCurrency}
                onChange={handleInputChange}
                required
                style={styles.input}
              >
                <option value="-1">Please select a currency</option>
                {loadCurrencyFrom?.map((currency, index) => (
                  <option key={index} value={currency.currency_code}>
                    {currency.currency_code}
                  </option>
                ))}
              </select>
            </div>

            <div style={styles.questionGroup}>
              <label style={styles.label} htmlFor="tradingVolume">
                What is your expected monthly trading volume (in $)?
              </label>
              <input
                type="number"
                id="tradingVolume"
                name="tradingVolume"
                value={responses.tradingVolume}
                onChange={handleInputChange}
                required
                min="0"
                style={styles.input}
              />
            </div>

            <div style={styles.questionGroup}>
              <label style={styles.label} htmlFor="country">
                In which country are you based?
              </label>
              {/* <input
                type="text"
                id="country"
                name="country"
                value={responses.country}
                onChange={handleInputChange}
                required
                style={styles.input}
              /> */}
              <select
                style={styles.input}
                name="country"
                id="country"
                onChange={handleInputChange}
              >
                <option value="-1">Select your country</option>
                {phoneCountryArray.map((el) => (
                  <option value={el.country_name}>{el.country_name}</option>
                ))}
              </select>
            </div>

            <button type="submit" style={styles.submitButton}>
              Submit Responses
            </button>
          </form>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
}
