"use client";
import React, { useEffect, useState, useRef } from "react";
import Chart from "chart.js";
import styles from "./graph.module.css";
import { getTransactionOverview } from "@/app/api/DashboardApi/transactionsGraph";

const TransactionGraph = () => {
  const [timeRange, setTimeRange] = useState("weekly");
  const [isLoading, setIsLoading] = useState(false);
  const [apiData, setApiData] = useState(null);
  const [error, setError] = useState(null);
  const [isDataCached, setIsDataCached] = useState(false);
  const chartRef = useRef(null);
  const canvasRef = useRef(null);

  // Cache configuration
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
  const CACHE_KEY_PREFIX = 'transaction_overview_';

  // Function to get cache key for a specific period
  const getCacheKey = (period) => `${CACHE_KEY_PREFIX}${period}`;

  // Function to get cached data
  const getCachedData = (period) => {
    try {
      const cacheKey = getCacheKey(period);
      const cachedItem = sessionStorage.getItem(cacheKey);
      
      if (!cachedItem) return null;
      
      const { data, timestamp } = JSON.parse(cachedItem);
      const now = Date.now();
      
      // Check if cache is still valid
      if (now - timestamp < CACHE_DURATION) {
        return data;
      } else {
        // Cache expired, remove it
        sessionStorage.removeItem(cacheKey);
        return null;
      }
    } catch (error) {
      console.error('Error reading from cache:', error);
      return null;
    }
  };

  // Function to set cached data
  const setCachedData = (period, data) => {
    try {
      const cacheKey = getCacheKey(period);
      const cacheItem = {
        data,
        timestamp: Date.now()
      };
      sessionStorage.setItem(cacheKey, JSON.stringify(cacheItem));
    } catch (error) {
      console.error('Error writing to cache:', error);
    }
  };

  // Function to clear all cached data
  const clearCache = () => {
    try {
      ['weekly', 'monthly', 'yearly'].forEach(period => {
        sessionStorage.removeItem(getCacheKey(period));
      });
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  };

  // Function to fetch transaction data from API with caching
  const fetchTransactionData = async (period) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Check for cached data first
      const cachedData = getCachedData(period);
      if (cachedData) {
        console.log(`Using cached data for ${period}`);
        setApiData(cachedData);
        setIsDataCached(true);
        setIsLoading(false);
        return;
      }
      
      console.log(`Fetching fresh data for ${period}`);
      const response = await getTransactionOverview(period);
      
      if (response.success) {
        // Cache the successful response
        setCachedData(period, response.data);
        setApiData(response.data);
        setIsDataCached(false);
      } else {
        throw new Error('API response indicates failure');
      }
    } catch (error) {
      console.error('Error fetching transaction data:', error);
      setError('Failed to load transaction data');
      // Fallback to demo data on error
      setApiData(getDefaultData(period));
    } finally {
      setIsLoading(false);
    }
  };

  // Fallback data in case API fails
  const getDefaultData = (period) => {
    switch (period) {
      case "monthly":
        return {
          transactions: [23, 37, 45, 53, 60, 76, 43],
          labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
          currency: "USD",
          time: "monthly"
        };
      case "yearly":
        return {
          transactions: [230, 378, 456, 534, 600, 764, 432],
          labels: ["Q1", "Q2", "Q3", "Q4", "Q5", "Q6", "Q7"],
          currency: "USD",
          time: "yearly"
        };
      default:
        return {
          transactions: [11, 12, 16, 15, 33, 0, 6],
          labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
          currency: "USD",
          time: "weekly"
        };
    }
  };

  useEffect(() => {
    fetchTransactionData(timeRange);
  }, [timeRange]);

  useEffect(() => {
    if (!apiData) return;

    const updateGraph = () => {
      // Simulate loading delay for smooth UX
      setTimeout(() => {
        let gradientColors;

        switch (timeRange) {
          case "monthly":
            gradientColors = ["#3B82F6", "#10B981"];
            break;
          case "yearly":
            gradientColors = ["#8B5CF6", "#EC4899"];
            break;
          default:
            gradientColors = ["#10B981", "#3B82F6"];
        }

        const ctx = canvasRef.current?.getContext("2d");
        if (!ctx) return;

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, gradientColors[0]);
        gradient.addColorStop(1, gradientColors[1]);

        // Create hover gradient
        const hoverGradient = ctx.createLinearGradient(0, 0, 0, 300);
        hoverGradient.addColorStop(0, "#F59E0B");
        hoverGradient.addColorStop(1, "#EF4444");

        const config = {
          type: "bar",
          data: {
            labels: apiData.labels,
            datasets: [
              {
                label: "Transaction Volume",
                backgroundColor: gradient,
                hoverBackgroundColor: hoverGradient,
                borderColor: "transparent",
                borderWidth: 0,
                data: apiData.transactions,
                borderRadius: 8,
                borderSkipped: false,
                barThickness: 24,
                maxBarThickness: 32,
              },
            ],
          },
          options: {
            maintainAspectRatio: false,
            responsive: true,
            animation: {
              duration: 1200,
              easing: 'easeInOutQuart',
            },
            interaction: {
              intersect: false,
              mode: 'index',
            },
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                titleColor: '#1E293B',
                bodyColor: '#64748B',
                borderColor: 'rgba(59, 130, 246, 0.2)',
                borderWidth: 1,
                cornerRadius: 12,
                displayColors: false,
                titleFont: {
                  size: 14,
                  weight: '600',
                },
                bodyFont: {
                  size: 13,
                  weight: '500',
                },
                padding: 12,
                callbacks: {
                  title: function(context) {
                    return context[0].label;
                  },
                  label: function(context) {
                    return `${context.parsed.y} transactions`;
                  }
                }
              },
            },
            scales: {
              x: {
                display: true,
                grid: {
                  display: false,
                },
                border: {
                  display: false,
                },
                ticks: {
                  color: '#64748B',
                  font: {
                    size: 12,
                    weight: '500',
                  },
                  padding: 8,
                },
              },
              y: {
                display: true,
                grid: {
                  color: 'rgba(148, 163, 184, 0.1)',
                  drawBorder: false,
                },
                border: {
                  display: false,
                },
                ticks: {
                  color: '#64748B',
                  font: {
                    size: 11,
                    weight: '500',
                  },
                  padding: 12,
                  callback: function(value) {
                    return value + ' txns';
                  }
                },
              },
            },
          },
        };

        // Destroy existing chart
        if (chartRef.current) {
          chartRef.current.destroy();
        }

        // Create new chart
        chartRef.current = new Chart(ctx, config);
      }, 300);
    };

    updateGraph();

    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [apiData, timeRange]);

  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };

  // Function to refresh data (bypass cache)
  const handleRefresh = async () => {
    // Clear cache for current period and refetch
    sessionStorage.removeItem(getCacheKey(timeRange));
    await fetchTransactionData(timeRange);
  };

  const formatTimeRangeLabel = (range) => {
    switch (range) {
      case "monthly": return "Monthly View";
      case "yearly": return "Yearly View";
      default: return "Weekly View";
    }
  };

  return (
    <div className={styles.graphWrapper}>
      {/* Header Section */}
      <div className={styles.graphHeader}>
        <div className={styles.headerContent}>
          <div className={styles.titleSection}>
            <h2 className={styles.graphTitle}>Transaction Overview</h2>
            <p className={styles.graphSubtitle}>Track your financial activity</p>
          </div>
          <div className={styles.controlsSection}>
            <div className={styles.timeRangeSelector}>
              <label className={styles.selectorLabel}>Period:</label>
              <select
                value={timeRange}
                onChange={handleTimeRangeChange}
                className={styles.modernSelect}
                disabled={isLoading}
              >
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className={styles.refreshButton}
              title="Refresh data"
            >
              <span className={styles.refreshIcon}>↻</span>
            </button>
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className={styles.chartContainer}>
        <div className={styles.chartWrapper}>
          {isLoading && (
            <div className={styles.loadingOverlay}>
              <div className={styles.loadingSpinner}></div>
              <span className={styles.loadingText}>Updating chart...</span>
            </div>
          )}
          {error && (
            <div className={styles.errorMessage}>
              <span>{error}</span>
            </div>
          )}
          <canvas
            ref={canvasRef}
            className={styles.chartCanvas}
          ></canvas>
        </div>

        {/* Chart Footer */}
        <div className={styles.chartFooter}>
          <div className={styles.chartInfo}>
            <span className={styles.currentPeriod}>{formatTimeRangeLabel(timeRange)}</span>
            <span className={styles.dataSource}>
              {apiData && (
                <>
                  {apiData.currency} • {isDataCached ? 'Cached data' : 'Real-time data'}
                  {isDataCached && <span className={styles.cacheIndicator}>📋</span>}
                </>
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionGraph;
