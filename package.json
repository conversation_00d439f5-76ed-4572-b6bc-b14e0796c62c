{"name": "remflow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rd /s /q .next"}, "dependencies": {"@iconify/react": "^5.0.2", "@sentry/nextjs": "^8.26.0", "axios": "^1.4.0", "chart.js": "^2.9.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "dotenv": "^16.3.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "next": "^13.4.6", "nprogress": "^0.2.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-form-stepper": "^2.0.3", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-modal": "^3.16.1", "react-paginate": "^8.1.3", "react-toastify": "^9.1.3", "react-tooltip": "^5.26.3", "react-use-websocket": "^4.8.1", "react-webcam": "^7.2.0", "socket.io-client": "^4.7.5", "stream-chat": "^8.14.3", "stream-chat-react": "^11.0.0", "universal-cookie": "^6.1.1", "websocket": "^1.0.34"}}