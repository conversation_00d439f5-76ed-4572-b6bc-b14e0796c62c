import axios from "axios";

const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

export const getCountryAPi = async () => {
  const response = await axios({
    url: `${BaseURL}/country-list/`,
    method: "GET",
  });
  return response;
};

export const getCountryTOapi = async () => {
  const response = await axios({
    url: `${BaseURL}/country-list/?flag=to`,
    method: "GET",
  });
  return response;
};

export const getCurrencyListByCountry = async (Data) => {
  const response = await axios.get(`${BaseURL}/select-country-get-currency/`, {
    params: {
      country: Data,
    },
  });
  return response;
};
