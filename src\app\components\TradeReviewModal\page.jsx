"use client";
import { useState, useEffect } from "react";
import styles from "./TradeReviewModal.module.css";
import { toast } from "react-toastify";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const TradeReviewModal = ({ closeModal, onClose, orderNumber, username }) => {
  const handleClose = closeModal || onClose;

  const [rating, setRating] = useState(3);
  const [review, setReview] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchFeedbackQuestions();
  }, []);

  const fetchFeedbackQuestions = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await customFetchWithToken(
        "/get-base-feedback-question/"
      );

      // customFetchWithToken likely returns the data directly, not a Response object
      // so we need to check the structure differently
      if (response?.data?.results) {
        setQuestions(response.data.results);
        // Initialize rating based on the first question if it's a rating type
        const ratingQuestion = response.data.results.find(
          (q) => q.option.value === "select" && q.option.options?.includes("5")
        );
        if (ratingQuestion) {
          setRating(3); // Default to middle rating
        }
      } else {
        console.error("Invalid response format:", response);
        throw new Error("Invalid response format");
      }
    } catch (err) {
      console.error("Error fetching feedback questions:", err);
      setError("Failed to load feedback questions");
    } finally {
      setLoading(false);
    }
  };

  const handleRating = (value) => {
    setRating(value);
  };

    const handleSubmit = async () => {
    if (rating < 1 || rating > 5) {
      toast.error("Please select a rating between 1 and 5");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Submit the review
      await customFetchWithToken('/create-review/', {
        method: 'POST',
        data: {
          total_star: 5,
          get_star: rating
        },
      });

      console.log("Review submitted successfully");
      toast.success("Review submitted successfully");
      handleClose();
    } catch (err) {
      console.error("Error submitting review:", err);
      setError("Failed to submit review. Please try again.");
      toast.error("Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get the rating and comment questions
  const ratingQuestion = questions.find(
    (q) => q.option.value === "select" && q.option.options?.includes("5")
  );

  const commentQuestion = questions.find((q) => q.option.value === "text");

  const ratingTexts = ["Terrible", "Bad", "Okay", "Good", "Great"];

  return (
    <div className={styles.reviewModalContainer}>
      <h2 className={styles.modalHeader}>
        Rate Your Trading Experience with {username}
        <span
          className={styles.closeButton}
          onClick={() => {
            console.log("Close button clicked");
            if (typeof handleClose === "function") {
              handleClose();
            } else {
              console.error("No close function available");
            }
          }}
        >
          ✕
        </span>
      </h2>

      {loading ? (
        <div style={{ padding: "20px", textAlign: "center" }}>Loading...</div>
      ) : error ? (
        <div style={{ padding: "20px", color: "red", textAlign: "center" }}>
          {error}
        </div>
      ) : (
        <>
          {ratingQuestion && (
            <div className={styles.ratingContainer}>
              <p>{ratingQuestion.question_name}</p>
              <div className={styles.starsContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <span
                    key={star}
                    className={`${styles.star} ${
                      rating >= star ? styles.filledStar : ""
                    }`}
                    onClick={() => handleRating(star)}
                  >
                    ★
                  </span>
                ))}
              </div>
              <div
                style={{ textAlign: "center", marginTop: "5px", color: "#666" }}
              >
                {ratingTexts[rating - 1]}
              </div>
            </div>
          )}

          {commentQuestion && (
            <div className={styles.reviewContainer}>
              <p>{commentQuestion.question_name}</p>
              <textarea
                className={styles.reviewTextarea}
                value={review}
                onChange={(e) => setReview(e.target.value)}
                placeholder="Add your comments here"
                rows={4}
              />
            </div>
          )}
        </>
      )}

      <div className={styles.buttonContainer}>
        <button
          className={styles.submitButton}
          onClick={handleSubmit}
          disabled={isSubmitting || loading || !ratingQuestion}
          style={{ width: "100%" }}
        >
          {isSubmitting ? "Submitting..." : "Submit Review"}
        </button>
      </div>
    </div>
  );
};

export default TradeReviewModal;
