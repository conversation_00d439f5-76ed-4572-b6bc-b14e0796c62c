# CountDownTimer Component

A modern, responsive countdown timer component designed for trade pages that displays time remaining when trade status is active.

## Features

- **Conditional Rendering**: Only shows when trade status is active
- **Real-time Countdown**: Updates every second with smooth animations
- **Modern Design**: Clean, professional UI with gradients and animations
- **Responsive**: Adapts to mobile, tablet, and desktop screens
- **Status Indicator**: Shows active trade status with animated dot
- **Accessibility**: Supports reduced motion preferences and high contrast mode

## Usage

```jsx
import CountDownTimer from "@/app/components/CountDownTimer/page";

// Basic usage
<CountDownTimer
  duration={timeLimit?.left_time_in_milliseconds}
  tradeStatus={timeLimit?.status}
  className={styles.countDownTimerWrapper}
/>
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `duration` | number | Yes | Time remaining in milliseconds |
| `tradeStatus` | string | No | Current trade status (active, completed, expired, etc.) |
| `className` | string | No | Additional CSS classes |

## Trade Status Behavior

### Active Statuses (Timer Shows):
- `"active"` - Default active state
- `"pending"` - Waiting for action
- `"in_progress"` - Trade in progress
- `"waiting"` - Waiting for peer
- `"ongoing"` - Trade ongoing
- `undefined` - Defaults to active

### Inactive Statuses (Timer Hidden):
- `"completed"` - Trade finished
- `"expired"` - Trade expired
- `"rejected"` - Trade rejected

## Integration

The component is integrated into the trade page at:
- **Location**: `src/app/pages/trade/[id]/page.jsx`
- **Position**: Right side of the trade header (`topRightBox`)
- **Styling**: Uses `countDownTimerWrapper` class for positioning

## Styling

The component uses CSS modules with:
- **File**: `countDownTimer.module.css`
- **Font**: Poppins for modern typography
- **Colors**: Blue gradients for active elements, red gradient for timer display
- **Animations**: Slide-in, pulse, and blink effects
- **Responsive**: Mobile-first design with breakpoints at 768px and 480px

## Example Output

When active, displays:
```
⏰ Time Remaining
   02:45:30
   🟢 ACTIVE TRADE
```

When inactive (completed/expired), the component doesn't render.
