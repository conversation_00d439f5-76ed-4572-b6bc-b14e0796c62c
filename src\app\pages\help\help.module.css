/* Main Container */
.rightContainerBody {
    width: 100%;
    height: 100%;
    padding: 30px;
    border-radius: 20px;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: calc(100vh - 120px);
}

@media screen and (max-width: 576px) {
    .rightContainerBody {
        padding: 15px 5px;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
    }
}

/* Help Container */
.helpContainer {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

@media screen and (max-width: 576px) {
    .helpContainer {
        width: 98%;
        gap: 20px;
    }
}

/* Mobile Header Section */
.mobileHeader {
    display: none;
}

@media (max-width: 576px) {
    .mobileHeader {
        display: block;
        margin-bottom: 20px;
        padding: 0 16px;
        text-align: center;
    }
}

.headerContent {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.pageTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

@media screen and (max-width: 576px) {
    .pageTitle {
        font-size: 24px;
        text-align: center;
        width: 100%;
    }
}

.pageSubtitle {
    font-size: 14px;
    color: #64748B;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

@media (max-width: 576px) {
    .pageSubtitle {
        font-size: 13px;
    }
}

/* Welcome Section */
.welcomeSection {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .welcomeSection {
        padding: 25px 20px;
        border-radius: 16px;
    }
}

.welcomeIcon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.8;
}

@media screen and (max-width: 576px) {
    .welcomeIcon {
        font-size: 48px;
        margin-bottom: 15px;
    }
}

.welcomeTitle {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 15px 0;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

@media screen and (max-width: 576px) {
    .welcomeTitle {
        font-size: 24px;
        margin-bottom: 12px;
    }
}

.welcomeDescription {
    font-size: 16px;
    color: #64748b;
    line-height: 1.6;
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

@media screen and (max-width: 576px) {
    .welcomeDescription {
        font-size: 14px;
        line-height: 1.5;
    }
}

/* FAQ Section */
.faqSection {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .faqSection {
        padding: 20px 15px;
        border-radius: 16px;
    }
}

.faqTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 25px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

@media screen and (max-width: 576px) {
    .faqTitle {
        font-size: 20px;
        margin-bottom: 20px;
        justify-content: center;
        text-align: center;
    }
}

.faqIcon {
    font-size: 24px;
    opacity: 0.8;
}

/* Accordion Styles */
.accordionContainer {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.accordionItem {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.accordionItem:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.accordionHeader {
    width: 100%;
    padding: 20px 24px;
    background: transparent;
    border: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    font-family: inherit;
    transition: all 0.3s ease;
}

@media screen and (max-width: 576px) {
    .accordionHeader {
        padding: 16px 18px;
        gap: 12px;
    }
}

.accordionHeader:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.accordionHeaderActive {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.accordionHeaderActive:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.accordionQuestion {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    flex: 1;
}

@media screen and (max-width: 576px) {
    .accordionQuestion {
        font-size: 14px;
        line-height: 1.3;
    }
}

.accordionIcon {
    font-size: 14px;
    transition: transform 0.3s ease;
    opacity: 0.7;
    flex-shrink: 0;
}

.accordionIconRotated {
    transform: rotate(180deg);
}

.accordionContent {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.accordionContentActive {
    max-height: 500px;
    border-top: 1px solid #e2e8f0;
}

.accordionAnswer {
    padding: 20px 24px;
    font-size: 15px;
    line-height: 1.6;
    color: #475569;
}

@media screen and (max-width: 576px) {
    .accordionAnswer {
        padding: 16px 18px;
        font-size: 14px;
        line-height: 1.5;
    }
}

/* Contact Section */
.contactSection {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.contactCard {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    max-width: 500px;
    width: 100%;
    transition: all 0.3s ease;
}

@media screen and (max-width: 576px) {
    .contactCard {
        padding: 25px 20px;
        border-radius: 16px;
        width: 98%;
        margin: 0 auto;
    }
}

.contactCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.contactIcon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.8;
}

@media screen and (max-width: 576px) {
    .contactIcon {
        font-size: 40px;
        margin-bottom: 15px;
    }
}

.contactTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 12px 0;
}

@media screen and (max-width: 576px) {
    .contactTitle {
        font-size: 20px;
        margin-bottom: 10px;
    }
}

.contactDescription {
    font-size: 16px;
    color: #64748b;
    line-height: 1.6;
    margin: 0 0 25px 0;
}

@media screen and (max-width: 576px) {
    .contactDescription {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 20px;
    }
}

.contactButton {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    outline: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 16px;
    padding: 14px 28px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0 auto;
    font-family: inherit;
}

@media screen and (max-width: 576px) {
    .contactButton {
        width: 100%;
        font-size: 14px;
        padding: 12px 24px;
    }
}

.contactButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.contactButton:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.buttonIcon {
    font-size: 16px;
}

@media screen and (max-width: 576px) {
    .buttonIcon {
        font-size: 14px;
    }
}

/* Accessibility Improvements */
.accordionHeader:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.contactButton:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

/* Animation for smooth accordion transitions */
@keyframes accordionSlideDown {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 500px;
        opacity: 1;
    }
}

@keyframes accordionSlideUp {
    from {
        max-height: 500px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

.accordionContentActive {
    animation: accordionSlideDown 0.3s ease-out;
}

/* Responsive adjustments for very small screens */
@media screen and (max-width: 320px) {
    .rightContainerBody {
        padding: 10px 3px;
    }

    .helpContainer {
        width: 100%;
        gap: 15px;
    }

    .welcomeSection,
    .faqSection,
    .contactCard {
        padding: 20px 15px;
        border-radius: 12px;
    }

    .accordionHeader {
        padding: 14px 16px;
    }

    .accordionAnswer {
        padding: 14px 16px;
    }
}

/* Support Tickets Section */
.ticketsSection {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .ticketsSection {
        padding: 20px 15px;
        border-radius: 16px;
    }
}

.ticketsTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 25px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.ticketsTitleButton {
    width: 100%;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    justify-content: space-between;
    font-family: inherit;
    padding: 0;
    text-align: left;
}

.ticketsTitleButton:hover {
    color: #3b82f6;
}

.ticketsTitleActive {
    color: #3b82f6;
}

.ticketsTitleLeft {
    display: flex;
    align-items: center;
    gap: 12px;
}

@media screen and (max-width: 576px) {
    .ticketsTitle {
        font-size: 20px;
        margin-bottom: 20px;
    }
    
    .ticketsTitleButton {
        flex-direction: column;
        gap: 12px;
        align-items: center;
        text-align: center;
    }
    
    .ticketsTitleLeft {
        justify-content: center;
    }
}

.ticketsIcon {
    font-size: 24px;
    opacity: 0.8;
}

.ticketCount {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    font-size: 14px;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 12px;
    margin-left: 8px;
}

.ticketsContainer {
    width: 100%;
    overflow: hidden;
    transition: max-height 0.4s ease, opacity 0.3s ease;
}

.ticketsContainerOpen {
    max-height: 2000px;
    opacity: 1;
}

.ticketsContainerClosed {
    max-height: 0;
    opacity: 0;
}

.ticketsToggleIcon {
    font-size: 18px;
    color: #64748b;
    transition: transform 0.3s ease;
    margin-left: 12px;
}

.ticketsToggleIconRotated {
    transform: rotate(180deg);
}

/* Loading State */
.loadingState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #64748b;
}

.loadingSpinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.emptyState {
    text-align: center;
    padding: 40px 20px;
    color: #64748b;
}

.emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.emptyState h4 {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.emptyState p {
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* Tickets Accordion */
.ticketsAccordion {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ticketItem {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.ticketItem:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ticketHeader {
    width: 100%;
    background: none;
    border: none;
    padding: 20px;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.2s ease;
    font-family: inherit;
}

@media screen and (max-width: 576px) {
    .ticketHeader {
        padding: 16px;
        flex-direction: column;
        gap: 12px;
    }
}

.ticketHeader:hover {
    background: #f8fafc;
}

.ticketHeaderActive {
    background: #f1f5f9;
    border-bottom: 1px solid #e2e8f0;
}

.ticketHeaderLeft {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ticketSubject {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.4;
}

@media screen and (max-width: 576px) {
    .ticketSubject {
        font-size: 15px;
    }
}

.ticketMeta {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

@media screen and (max-width: 576px) {
    .ticketMeta {
        gap: 8px;
    }
}

.ticketStatus {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ticketStatus.open {
    background: #fef3c7;
    color: #92400e;
}

.ticketStatus.resolved {
    background: #d1fae5;
    color: #065f46;
}

.ticketDate {
    font-size: 13px;
    color: #64748b;
    font-weight: 500;
}

.ticketIcon {
    font-size: 16px;
    color: #64748b;
    transition: transform 0.2s ease;
    margin-top: 2px;
}

.ticketIconRotated {
    transform: rotate(180deg);
}

@media screen and (max-width: 576px) {
    .ticketIcon {
        align-self: center;
    }
}

.ticketContent {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.ticketContentActive {
    max-height: 800px;
    padding: 0 20px 20px 20px;
}

@media screen and (max-width: 576px) {
    .ticketContentActive {
        padding: 0 16px 16px 16px;
    }
}

.ticketBody {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
}

@media screen and (max-width: 576px) {
    .ticketBody {
        padding: 16px;
    }
}

.ticketBodySection,
.ticketResponseSection {
    margin-bottom: 20px;
}

.ticketResponseSection {
    background: #ffffff;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #10b981;
}

.ticketBodySection h5,
.ticketResponseSection h5 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.ticketBodySection p,
.ticketResponseSection p {
    font-size: 14px;
    color: #4b5563;
    line-height: 1.6;
    margin: 0;
    white-space: pre-wrap;
}

.ticketDetails {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.ticketDetails small {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

/* Focus styles for accessibility */
.ticketHeader:focus {
    outline: none;
}

.ticketsTitleButton:focus {
    outline: none;
    border-radius: 8px;
}