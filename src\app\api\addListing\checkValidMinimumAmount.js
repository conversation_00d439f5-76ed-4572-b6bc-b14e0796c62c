import axios from "axios";
require("dotenv").config();

const BaseUrl = process.env.NEXT_PUBLIC_Base_URL;

const getCheckMin = async (from_currency, min_liquidity) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const res = await axios({
    url: `${BaseUrl}/minimum-amount-check/?from_currency=${from_currency}&amount=${min_liquidity}`,
    // headers: {
    //   Authorization: `Bearer ${token}`,
    // },
    method: "GET",
  });
  return res;
};

export default getCheckMin;
