"use client";
import React from 'react';
import { JssProvider, createGenerateId } from 'react-jss';

// Create a custom generateId function to avoid conflicts
const generateId = createGenerateId({
  minify: process.env.NODE_ENV === 'production'
});

const JSSProvider = ({ children }) => {
  return (
    <JssProvider generateId={generateId}>
      {children}
    </JssProvider>
  );
};

export default JSSProvider;
