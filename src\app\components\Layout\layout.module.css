@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap");

/* Main container */
.main {
    background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
    min-height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    font-family: 'Poppins', sans-serif;
}

/* Mobile hamburger menu - hidden by default */
.hamMenu {
    display: none;
    width: 100%;
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%);
    position: fixed;
    top: 0;
    height: 56px;
    z-index: 1200;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
}

/* Show hamburger menu on mobile */
@media screen and (max-width: 576px) {
    .hamMenu {
        display: flex;
    }
}

/* Hamburger menu left button */
.hamMenuLeft {
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 12px;
    width: 36px;
    height: 36px;
    position: relative;
    z-index: 2;
}

@media screen and (max-width: 576px) {
    .hamMenuLeft {
        display: flex;
    }
}

.hamMenuLeft:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
}

.hamMenuLeft svg {
    width: 20px;
    height: 20px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Hamburger menu center */
.hamMenuCenter {
    display: none;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: relative;
    z-index: 2;
}

@media screen and (max-width: 576px) {
    .hamMenuCenter {
        display: flex;
    }
}

.brandText {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    text-align: center;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Hamburger menu right */
.hamMenuRight {
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    height: 100%;
}

@media screen and (max-width: 576px) {
    .hamMenuRight {
        display: flex;
    }
}

/* Menu overlay */
.menuOverlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 800;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.4s ease;
}

@media screen and (max-width: 576px) {
    .menuOverlay {
        display: block;
    }
}

.menuOverlayVisible {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 800;
    opacity: 1;
    pointer-events: auto;
    transition: opacity 0.4s ease;
}

@media screen and (max-width: 576px) {
    .menuOverlayVisible {
        display: block;
    }
}

/* Main wrapper */
.wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
    margin: auto;
    justify-content: space-between;
}

/* Left sidebar container */
.leftContainerWrapper {
    width: 20%;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1100;
    padding: 30px 10px;
    transform: translateX(0);
    transition: transform 0.4s ease;
}

@media screen and (max-width: 576px) {
    .leftContainerWrapper {
        width: 85%;
        max-width: 320px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(20px);
        padding: 56px 0 0 0;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.12), 4px 0 16px rgba(0, 0, 0, 0.08);
        border-right: 1px solid rgba(99, 102, 241, 0.1);
    }
}

.leftContainerWrapperHide {
    width: 20%;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1100;
    padding: 30px 10px;
    transform: translateX(0);
    transition: transform 0.4s ease;
}

@media screen and (max-width: 576px) {
    .leftContainerWrapperHide {
        width: 85%;
        max-width: 320px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(20px);
        padding: 56px 0 0 0;
        box-shadow: 8px 0 32px rgba(0, 0, 0, 0.12), 4px 0 16px rgba(0, 0, 0, 0.08);
        border-right: 1px solid rgba(99, 102, 241, 0.1);
        transform: translateX(-100%);
    }
}

.leftContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
}

@media screen and (max-width: 576px) {
    .leftContainer {
        border-radius: 0;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(99, 102, 241, 0.3) transparent;
    }
}

/* Custom scrollbar */
.leftContainer::-webkit-scrollbar {
    width: 6px;
}

.leftContainer::-webkit-scrollbar-track {
    background: transparent;
}

.leftContainer::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.leftContainer::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #7c3aed);
}

/* Right container */
.rightContainer {
    width: 75%;
    height: 100vh;
    display: flex;
    margin-left: 22%;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

@media screen and (max-width: 576px) {
    .rightContainer {
        width: 100%;
        margin-left: 0;
    }
}

/* Hide scrollbar */
.rightContainer::-webkit-scrollbar {
    display: none;
}

.rightContainer {
    scrollbar-width: none;
}

.rightContainerWrapper {
    width: 100%;
    color: #000;
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    padding: 30px 10px;
    min-height: 100vh;
}

@media screen and (max-width: 576px) {
    .rightContainerWrapper {
        padding: 70px 0px 30px 0px;
    }
}

.rightContainerBox {
    display: flex;
    justify-content: space-between;
}

.rightContainerHeader {
    height: 94px;
    color: #000;
    font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
}

@media screen and (max-width: 576px) {
    .rightContainerHeader {
        display: none;
    }
}

.rightContainerBody {
    min-height: calc(100vh - 94px);
    padding: 30px;
    border-radius: 20px;
    background: #fff;
    margin-bottom: 20px;
}

@media screen and (max-width: 576px) {
    .rightContainerBody {
        padding: 20px;
        min-height: calc(100vh - 140px);
        margin-top: 16px;
        border-radius: 0;
    }
}

.body {
    height: 100%;
    background-color: #fff;
    width: 100%;
}

/* Desktop notification wrapper */
.notificationWrapper {
    margin-right: 50px;
    display: flex;
    justify-content: center;
    position: relative;
    transition: 1s linear;
}

@media screen and (max-width: 576px) {
    .notificationWrapper {
        display: none;
    }
}

/* Desktop title wrapper */
.desktopTitleWrapper {
    display: block;
}

@media screen and (max-width: 576px) {
    .desktopTitleWrapper {
        display: none;
    }
}

/* Mobile welcome message */
.mobileWelcome {
    display: none;
    flex-direction: column;
    gap: 12px;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.12), rgba(118, 75, 162, 0.12));
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

@media screen and (max-width: 576px) {
    .mobileWelcome {
        display: flex;
    }
}

.welcomeHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.welcomeText {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
    margin: 0;
}

.mobileStatus {
    display: flex;
    align-items: center;
}

.statusBadge {
    display: inline-flex;
    align-items: center;
    border: none;
    padding: 0;
    background: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.badgeContent {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.badgeIcon {
    width: 14px;
    height: 14px;
}

.badgeText {
    white-space: nowrap;
}

/* Badge variants */
.badge--success .badgeContent {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.badge--pending .badgeContent {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.badge--warning .badgeContent {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.badge--review .badgeContent {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Notification styles */
.rightContainerBell {
    margin-right: 50px;
    display: flex;
    justify-content: center;
    position: relative;
    transition: 1s linear;
}

.icon_button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    color: #333333;
    background: #dddddd;
    border: none;
    outline: none;
    border-radius: 50%;
    cursor: pointer;
}

.icon_button:hover {
    cursor: pointer;
}

.icon_button:active {
    background: #cccccc;
}

.icon_button__badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 25px;
    height: 25px;
    background: red;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

.notificationLists {
    position: absolute;
    height: 300px;
    width: 300px;
    background-color: #4153ED;
    top: 40px;
    right: 0px;
    border-radius: 12px;
    z-index: 1000;
    transition: height 1s linear, opacity 1s linear;
    opacity: 1;
    overflow: hidden;
}

@media screen and (max-width: 576px) {
    .notificationLists {
        z-index: 99999;
    }
}

/* Form styles */
.firstformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
}

@media screen and (max-width: 576px) {
    .firstformWrapper {
        flex-direction: column;
    }
}

.secondformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    margin-top: 30px;
}

@media screen and (max-width: 576px) {
    .secondformWrapper {
        flex-direction: column;
    }
}

.thirdformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 30px;
}

@media screen and (max-width: 576px) {
    .thirdformWrapper {
        flex-direction: column;
    }
}

.firstName {
    width: 46%;
    height: 40px;
}

@media screen and (max-width: 576px) {
    .firstName {
        width: 100%;
        margin: 40px 0px;
    }
}

.firstNameLabel {
    font-size: 12px;
}

@media screen and (max-width: 576px) {
    .firstNameLabel {
        margin-bottom: 5px;
        margin-left: 17px;
    }
}

.sec_firstName {
    width: 20%;
    height: 40px;
}

@media screen and (max-width: 576px) {
    .sec_firstName {
        width: 100%;
        margin-bottom: 50px;
    }
}

.firstNameInput {
    display: flex;
}

@media screen and (max-width: 576px) {
    .firstNameInput {
        padding: 15px;
    }
}

.firstNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 40px;
}

@media screen and (max-width: 576px) {
    .firstNameInput input {
        margin-bottom: 5px;
    }
}

.firstNameInput select {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    height: 40px;
    padding-right: 10px;
}

.emailBtn {
    border-radius: 0px 2px 2px 0px;
    background: #f5f5f5;
    border: 1px solid #ebebeb;
    border-left: 2px solid #c4c3c3;
    font-size: 10px;
    font-weight: 600;
    width: 57px;
}

.addressName {
    width: 100%;
    margin-left: 23px;
}

.addressNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 96%;
    height: 40px;
    padding-left: 10px;
    padding-right: 10px;
}

@media screen and (max-width: 576px) {
    .addressNameInput input {
        width: 87%;
    }
}

.fourthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 55px;
}

.paymentBoxContainer {
    width: 60%;
    height: auto;
    border-radius: 15px;
    border: 1px solid #d9d9d9;
    background: #fff;
    margin: auto;
}

.paymentBox {
    background-color: #4f535a;
    width: 100%;
}

.paymentHeader {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding: 0px 15px;
    color: #000;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}

.paymentDirection {
    padding: 0px 15px;
    color: #000;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payFrom {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid black;
}

.payTo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid #efefef;
}

.fifthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: auto;
    margin-bottom: 20px;
}

.paymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.BitcoinpaymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.listing_BtnCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.listing_Btn {
    width: 365px;
    height: 50px;
    border-radius: 5px;
    border: 2px solid #4153ED;
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4153ED;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;
}

@media screen and (max-width: 576px) {
    .listing_Btn {
        margin-bottom: 30px;
    }
}

/* Timer overlay */
.overlayMsg {
    position: fixed;
    right: 10px;
    bottom: 30px;
    width: 300px;
    height: 100px;
    background-color: #1448d6fd;
    border-radius: 5px;
    color: #f3eded;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    padding: 30px 20px;
}

.cancelTimerBtn {
    margin-top: 10px;
    border: none;
    padding: 5px 10px;
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    border-radius: 5px;
    width: 100px;
    background-color: #f3eded;
    cursor: pointer;
}

/* Sidebar styles */
.sidebar {
    width: 100%;
    height: auto;
    border-radius: 15px;
    background: #fff;
}

.pages {
    margin-top: 30px;
    width: 100%;
    height: 100%;
}

.dashboardContainer {
    width: 100%;
    height: 47px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
    border-bottom: 1px solid #ececec;
}

.historyContainer {
    width: 100%;
    height: 47px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
}

.logoutContainer {
    width: 100%;
    height: 47px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #4153ed;
}

.sideIcons {
    margin-left: 20px;
    margin-right: 10px;
    color: #4153ed;
}

.dashboard {
    color: #4153ed;
}