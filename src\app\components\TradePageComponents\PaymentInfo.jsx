import React from 'react';
import { CopyToClipboard } from "react-copy-to-clipboard";
import { toast } from "react-toastify";
import styles from '../../pages/trade/search_results.module.css';

const PaymentInfo = ({ tradeData }) => {
  const handleCopyName = () => {
    toast.success("Name copied to clipboard!", {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  const handleCopyPaymentMethod = () => {
    toast.success("Payment method copied to clipboard!", {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  const handleCopyPaymentDetails = () => {
    toast.success("Payment details copied to clipboard!", {
      position: "top-right",
      autoClose: 2000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Helper function to get payment method text
  const getPaymentMethodText = () => {
    return tradeData?.data?.payin_option || "No payment method available";
  };

  // Helper function to get payment details text
  const getPaymentDetailsText = () => {
    if (Array.isArray(tradeData?.data?.payin_data)) {
      return tradeData.data.payin_data
        .map((item) => `${item.key}: ${item.value}`)
        .join("\n");
    } else if (typeof tradeData?.data?.payin_data === "string") {
      return tradeData.data.payin_data;
    } else if (tradeData?.data?.payin_data) {
      return JSON.stringify(tradeData.data.payin_data, null, 2);
    } else {
      return "No payment details available";
    }
  };

  const CopyIcon = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="11"
      height="12"
      viewBox="0 0 11 12"
      fill="none"
      style={{ cursor: "pointer" }}
    >
      <path
        d="M8.91253 10.3815H8.04721V11.1907H0.908292V2.89614H1.77362V2.08691H0.0429688V12H8.91253V10.3815Z"
        fill="#A5A5A5"
      />
      <path
        d="M2.12891 0V9.91304H10.9985V3.54631L7.19085 0H2.12891ZM10.1115 9.08696H3.01586V0.826087H5.89847V4.75H10.1115V9.08696ZM10.1115 3.92391H6.78543V0.826087H6.82346L10.1115 3.88847V3.92391Z"
        fill="#A5A5A5"
      />
    </svg>
  );

  return (
    <div className={styles.confirmOrderInfoArea}>
      <div className={styles.orderConfirmInfo}>
        <div className={styles.orderConfirmInfoSingle}>
          Currency Pair : {tradeData?.currency_from} - {tradeData?.currency_to}|
        </div>
        <div className={styles.orderConfirmInfoSingle}>
          Rate : {tradeData?.listing_data?.final_trade_fee?.toFixed(2)} |
        </div>
        <div className={styles.orderConfirmInfoSingle}>
          Amount : {tradeData?.trade_amount} |
        </div>
        <div className={styles.orderConfirmInfoSingle}>
          Limit : {tradeData?.listing_data?.min_liquidity} - {tradeData?.listing_data?.max_liquidity} |
        </div>
      </div>
      
      <div className={styles.orderDialogue}>
        Please make payment to this account to fund your trade with this peer.
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="14"
          height="12"
          viewBox="0 0 14 12"
          fill="none"
        >
          <path
            d="M7.01228 11.25C5.61342 11.25 4.27185 10.6969 3.2827 9.71231C2.29356 8.72774 1.73786 7.39239 1.73786 6C1.73786 4.60761 2.29356 3.27226 3.2827 2.28769C4.27185 1.30312 5.61342 0.75 7.01228 0.75C8.41114 0.75 9.75271 1.30312 10.7419 2.28769C11.731 3.27226 12.2867 4.60761 12.2867 6C12.2867 7.39239 11.731 8.72774 10.7419 9.71231C9.75271 10.6969 8.41114 11.25 7.01228 11.25ZM7.01228 12C8.61098 12 10.1442 11.3679 11.2747 10.2426C12.4051 9.11742 13.0402 7.5913 13.0402 6C13.0402 4.4087 12.4051 2.88258 11.2747 1.75736C10.1442 0.632141 8.61098 0 7.01228 0C5.41358 0 3.88036 0.632141 2.74991 1.75736C1.61946 2.88258 0.984375 4.4087 0.984375 6C0.984375 7.5913 1.61946 9.11742 2.74991 10.2426C3.88036 11.3679 5.41358 12 7.01228 12Z"
            fill="#7939EE"
          />
          <path
            d="M7.71375 4.941L5.98826 5.15625L5.92648 5.44125L6.26555 5.5035C6.48707 5.556 6.53078 5.6355 6.48255 5.85525L5.92648 8.45625C5.7803 9.129 6.0056 9.4455 6.5353 9.4455C6.94595 9.4455 7.42291 9.2565 7.63916 8.997L7.70546 8.685C7.55477 8.817 7.33475 8.8695 7.18857 8.8695C6.98136 8.8695 6.90601 8.72475 6.95951 8.46975L7.71375 4.941ZM7.7665 3.375C7.7665 3.57391 7.68711 3.76468 7.54581 3.90533C7.4045 4.04598 7.21285 4.125 7.01301 4.125C6.81317 4.125 6.62152 4.04598 6.48021 3.90533C6.33891 3.76468 6.25952 3.57391 6.25952 3.375C6.25952 3.17609 6.33891 2.98532 6.48021 2.84467C6.62152 2.70402 6.81317 2.625 7.01301 2.625C7.21285 2.625 7.4045 2.70402 7.54581 2.84467C7.68711 2.98532 7.7665 3.17609 7.7665 3.375Z"
            fill="#7939EE"
          />
        </svg>
      </div>
      
      <div className={styles.paymentInfoContainer}>
        <div className={styles.paymentMethodDisplay}>
          <div className={styles.paymentMethod}>
            {tradeData?.data?.payin_option}
          </div>
          <div className={styles.passedTerms}>
            {tradeData?.listing_data?.terms_and_conditions}
          </div>
        </div>

        <div className={styles.paymentAddressDisplay}>
          <div className={styles.paymentAddressName}>
            <div>
              Name-{" "}
              {tradeData?.flag === "user" ? (
                <span>
                  {tradeData?.peer_details?.firstname}{" "}
                  {tradeData?.peer_details?.lastname}
                </span>
              ) : (
                <span>
                  {tradeData?.user_details?.firstname}{" "}
                  {tradeData?.user_details?.lastname}
                </span>
              )}
            </div>
            <div>
              <CopyToClipboard
                text={
                  tradeData?.flag === "user"
                    ? `${tradeData?.peer_details?.firstname} ${tradeData?.peer_details?.lastname}`.trim()
                    : `${tradeData?.user_details?.firstname} ${tradeData?.user_details?.lastname}`.trim()
                }
                onCopy={handleCopyName}
              >
                <div title="Copy name to clipboard">
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            </div>
          </div>
          
          <div className={styles.paymentAddressName}>
            <div>
              Payment Method - {tradeData?.data?.payin_option}
            </div>
            <div>
              <CopyToClipboard
                text={getPaymentMethodText()}
                onCopy={handleCopyPaymentMethod}
              >
                <div title="Copy payment method to clipboard">
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            </div>
          </div>
          
          <div className={styles.paymentAddressName}>
            <div>
              Payment Details -{" "}
              {Array.isArray(tradeData?.data?.payin_data) ? (
                <div className={styles.tradePayDetails}>
                  {tradeData.data.payin_data.map((item, index) => (
                    <div key={index} className={styles.paymentDetailItem}>
                      <strong>{item.key}:</strong> {item.value}
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.paymentDetailItem}>
                  {typeof tradeData?.data?.payin_data === "string"
                    ? tradeData.data.payin_data
                    : tradeData?.data?.payin_data
                    ? JSON.stringify(tradeData.data.payin_data)
                    : "No payin details available"}
                </div>
              )}
            </div>
            <div>
              <CopyToClipboard
                text={getPaymentDetailsText()}
                onCopy={handleCopyPaymentDetails}
              >
                <div title="Copy payment details to clipboard">
                  <CopyIcon />
                </div>
              </CopyToClipboard>
            </div>
          </div>
        </div>
      </div>
      
      <div className={styles.uploadDialogue}>
        <svg
          className={styles.ctaDialogue}
          style={{ marginLeft: "5px" }}
          xmlns="http://www.w3.org/2000/svg"
          width="13"
          height="12"
          viewBox="0 0 13 12"
          fill="none"
        >
          <path
            d="M6.02791 11.25C4.62904 11.25 3.28748 10.6969 2.29833 9.71231C1.30918 8.72774 0.753488 7.39239 0.753488 6C0.753488 4.60761 1.30918 3.27226 2.29833 2.28769C3.28748 1.30312 4.62904 0.75 6.02791 0.75C7.42677 0.75 8.76834 1.30312 9.75748 2.28769C10.7466 3.27226 11.3023 4.60761 11.3023 6C11.3023 7.39239 10.7466 8.72774 9.75748 9.71231C8.76834 10.6969 7.42677 11.25 6.02791 11.25ZM6.02791 12C7.62661 12 9.15983 11.3679 10.2903 10.2426C11.4207 9.11742 12.0558 7.5913 12.0558 6C12.0558 4.4087 11.4207 2.88258 10.2903 1.75736C9.15983 0.632141 7.62661 0 6.02791 0C4.42921 0 2.89598 0.632141 1.76553 1.75736C0.635081 2.88258 0 4.4087 0 6C0 7.5913 0.635081 9.11742 1.76553 10.2426C2.89598 11.3679 4.42921 12 6.02791 12Z"
            fill="#7939EE"
          />
          <path
            d="M6.72938 4.941L5.00389 5.15625L4.9421 5.44125L5.28117 5.5035C5.5027 5.556 5.5464 5.6355 5.49818 5.85525L4.9421 8.45625C4.79593 9.129 5.02122 9.4455 5.55092 9.4455C5.96157 9.4455 6.43853 9.2565 6.65478 8.997L6.72109 8.685C6.57039 8.817 6.35037 8.8695 6.2042 8.8695C5.99699 8.8695 5.92164 8.72475 5.97514 8.46975L6.72938 4.941ZM6.78212 3.375C6.78212 3.57391 6.70274 3.76468 6.56143 3.90533C6.42012 4.04598 6.22847 4.125 6.02863 4.125C5.8288 4.125 5.63714 4.04598 5.49584 3.90533C5.35453 3.76468 5.27515 3.57391 5.27515 3.375C5.27515 3.17609 5.35453 2.98532 5.49584 2.84467C5.63714 2.70402 5.8288 2.625 6.02863 2.625C6.22847 2.625 6.42012 2.70402 6.56143 2.84467C6.70274 2.98532 6.78212 3.17609 6.78212 3.375Z"
            fill="#7939EE"
          />
        </svg>{" "}
        Please upload a receipt image in the chat to confirm payment has been sent.
      </div>
    </div>
  );
};

export default PaymentInfo;
