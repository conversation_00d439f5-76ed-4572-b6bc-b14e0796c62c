"use client";
import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import styles from "./layout.module.css";
import logoImg from "../../../../public/rem_logoW.png";
import ProfileImg from "../../../../public/assets/profile/profileImg.png";
import Sidebar from "../../components/sidebar/page";
import NotificationBox from "../NotificationBox/page";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useCheckStatusApihook } from "@/app/hooks/checkKycStatushook";
import { usePathname, useRouter } from "next/navigation";
import { useTimer } from "@/app/context/TimerContext";
import SessionModal from "@/app/components/SessionRefreshModal/page";
import { FaCheckCircle, FaExclamationCircle, FaHourglassHalf, FaUserShield } from "react-icons/fa";

const STATUS_CONFIG = {
  "User_Detail": {
    label: "Registration Pending",
    shortLabel: "Registering",
    icon: FaHourglassHalf,
    variant: "warning",
    priority: 1
  },
  "Document-Verifications": {
    label: "KYC Verification Required",
    shortLabel: "KYC Pending",
    icon: FaExclamationCircle,
    variant: "pending",
    priority: 2
  },
  "Admin-Verifications": {
    label: "Administrative Review",
    shortLabel: "Under Review",
    icon: FaUserShield,
    variant: "review",
    priority: 3
  },
  "Dash_Board": {
    label: "Fully Verified",
    shortLabel: "Verified",
    icon: FaCheckCircle,
    variant: "success",
    priority: 4
  },
};

const Page = ({ children, title }) => {
  const { isTimerActive, formattedTime, startTimer, stopTimer } = useTimer();
  const pathname = usePathname();
  const router = useRouter();
  const [hide, setHide] = useState(true);
  const [hideReason, setHideReason] = useState(false);
  const [status, setStatus] = useState("");
  const [notificationDrop, setNotificationDrop] = useState(false);
  const [firstName, setFirstName] = useState("");
  // Refactored: use state for browser-only values
  const [verifyStatus, setVerifyStatus] = useState(null);
  const [token, setToken] = useState(null);

  // Move browser-only code into useEffect
  useEffect(() => {
    if (typeof window !== "undefined") {
      setVerifyStatus(localStorage.getItem("verificationStatus"));
      setToken(sessionStorage.getItem("user"));
    }
  }, []);

  if (verifyStatus === "Document-Verifications") {
    useCheckStatusApihook();
  }

  const getVerificationStatusFunc = async () => {
    try {
      const res = await customFetchWithToken.get("/check-status/");
      setStatus(res.data.flag);
      localStorage.setItem("verificationStatus", res.data.flag);
    } catch (error) {
      console.log(error);
    }
  };

  const handleRedirect = () => {
    if (status === "Document-Verifications") {
      router.push("/verification/status");
    }
    if (status === "User_Detail") {
      router.push("/verification/personaldetails");
    }
  };

  const handleNotificationDrop = () => {
    setNotificationDrop(!notificationDrop);
  };

  const handleNotificationModalClose = () => {
    setNotificationDrop(false);
  };

  const hideHam = () => {
    setHide(!hide);
  };

  // Close menu when clicking overlay
  const handleOverlayClick = () => {
    setHide(true);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const VerificationStatus = localStorage.getItem("verificationStatus");
      setStatus(VerificationStatus);
    }
  }, []);

  useEffect(() => {
    if (pathname === "/pages/searchads" && token) {
      getVerificationStatusFunc();
    }
  }, [pathname, token]);

  // Update all references to verifyStatus and token to use state
  const handleRedirection = () => {
    if (verifyStatus === "User_Detail") {
      router.push("/verification/personaldetails");
    } else if (verifyStatus === "Document-Verifications") {
      router.push("/verification/status");
    } else if (!token) {
      router.push("/");
    }
  };

  // Fetch profile info
  const getProfileInfo = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");
      setFirstName(res.data.data.firstname);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (token) {
      getProfileInfo();
    }
  }, [token]);

  const currentStatus = STATUS_CONFIG[status];
  const StatusIcon = currentStatus?.icon;

  return (
    <div>
      <div className={styles.main}>
        <div className={styles.hamMenu}>
          <div className={styles.hamMenuLeft} onClick={hideHam}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              style={{
                transition: 'transform 0.3s ease'
              }}
            >
              {hide ? (
                <>
                  <line x1="3" y1="12" x2="21" y2="12" />
                  <line x1="3" y1="6" x2="21" y2="6" />
                  <line x1="3" y1="18" x2="21" y2="18" />
                </>
              ) : (
                <>
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </>
              )}
            </svg>
          </div>
          <div className={styles.hamMenuCenter}>
            <div>
              <h1 className={styles.brandText}>Remflow</h1>
            </div>
          </div>
          <div className={styles.hamMenuRight}>
            <NotificationBox
              handleNotificationDrop={handleNotificationDrop}
              notificationDrop={notificationDrop}
            />
          </div>
        </div>

        {/* Overlay */}
        <div 
          className={hide ? styles.menuOverlay : styles.menuOverlayVisible} 
          onClick={handleOverlayClick}
        />

        <div className={styles.wrapper}>
          <div className={hide ? styles.leftContainerWrapperHide : styles.leftContainerWrapper}>
            <div className={styles.leftContainer}>
              {/* Welcome Message for Mobile */}
              {token && (
                <div className={styles.mobileWelcome}>
                  <div className={styles.welcomeHeader}>
                    <span className={styles.welcomeText}>Welcome, {firstName || 'User'}</span>
                  </div>
                  {currentStatus && (
                    <div className={styles.mobileStatus}>
                      <span className={styles.statusLabel}>Account Status</span>
                      <div className={`${styles.statusBadge} ${styles[`badge--${currentStatus.variant}`]}`}>
                        <div className={styles.badgeContent}>
                          <StatusIcon className={styles.badgeIcon} aria-hidden="true" />
                          <span className={styles.badgeText}>
                            {currentStatus.shortLabel}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <Sidebar
                token={token}
                status={status}
                handleRedirect={handleRedirect}
              />
            </div>
          </div>
          
          <div className={styles.rightContainer}>
            <div className={styles.rightContainerWrapper}>
              <div className={styles.rightContainerBox}>
                <div className={styles.rightContainerHeader}>
                  {typeof title === 'string' ? title : (
                    <div className={styles.desktopTitleWrapper}>
                      {title}
                    </div>
                  )}
                </div>
                <div className={styles.notificationWrapper}>
                  <NotificationBox
                    handleNotificationDrop={handleNotificationDrop}
                    notificationDrop={notificationDrop}
                  />
                </div>
              </div>
              <div className={styles.rightContainerBody} onClick={handleNotificationModalClose}>
                <div className={styles.body}>{children}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
