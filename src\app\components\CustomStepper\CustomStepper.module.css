/* Import Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

.stepperContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  padding: 12px 0;
  overflow-x: auto;
  gap: 0;
}

.stepWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 0;
}

.stepCircle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 2;
  position: relative;
  background-color: white;
  margin-bottom: 8px;
}

.stepCircle.completed {
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.stepCircle.active {
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transform: scale(1.1);
}

.stepCircle.inactive {
  color: #6B7280;
  background-color: #F9FAFB;
}

.checkIcon {
  width: 14px;
  height: 14px;
}

.stepNumber {
  font-weight: 600;
  font-size: 12px;
}

.stepLabel {
  text-align: center;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.3s ease;
  line-height: 1.2;
  max-width: 100%;
  word-wrap: break-word;
  hyphens: auto;
  padding: 0 4px;
}

.labelCompleted {
  color: #10B981;
  font-weight: 600;
}

.labelActive {
  color: #3B82F6;
  font-weight: 600;
}

.labelInactive {
  color: #6B7280;
  font-weight: 400;
}

.connector {
  position: absolute;
  top: 16px;
  left: 50%;
  right: -50%;
  height: 2px;
  width: calc(100% - 16px);
  transition: all 0.3s ease;
  z-index: 1;
  transform: translateX(8px);
}

.connectorCompleted {
  background-color: #10B981;
}

.connectorInactive {
  background-color: #D1D5DB;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .stepperContainer {
    padding: 8px 0;
  }

  .stepCircle {
    width: 28px;
    height: 28px;
    margin-bottom: 6px;
  }

  .checkIcon {
    width: 12px;
    height: 12px;
  }

  .stepNumber {
    font-size: 10px;
  }

  .stepLabel {
    font-size: 10px;
    padding: 0 2px;
  }

  .connector {
    top: 14px;
    width: calc(100% - 12px);
    transform: translateX(6px);
  }
}

@media (max-width: 480px) {
  .stepperContainer {
    padding: 6px 0;
  }

  .stepCircle {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
  }

  .checkIcon {
    width: 10px;
    height: 10px;
  }

  .stepNumber {
    font-size: 9px;
  }

  .stepLabel {
    font-size: 9px;
    padding: 0 1px;
  }

  .connector {
    top: 12px;
    width: calc(100% - 8px);
    transform: translateX(4px);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .stepCircle,
  .stepLabel,
  .connector {
    transition: none;
  }

  .stepCircle.active {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stepCircle {
    border-width: 4px;
  }

  .stepCircle.inactive {
    background-color: #FFFFFF;
    border-color: #000000;
    color: #000000;
  }
}

/* Focus states for accessibility */
.stepCircle:focus-visible {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Hide connector on last step */
.stepWrapper:last-child .connector {
  display: none;
}

/* Compact mode for very tight spaces */
.stepperContainer.compact {
  padding: 4px 0;
}

.stepperContainer.compact .stepCircle {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
}

.stepperContainer.compact .checkIcon {
  width: 8px;
  height: 8px;
}

.stepperContainer.compact .stepNumber {
  font-size: 8px;
}

.stepperContainer.compact .stepLabel {
  font-size: 8px;
  padding: 0;
}

.stepperContainer.compact .connector {
  top: 10px;
  width: calc(100% - 6px);
  transform: translateX(3px);
}
