"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import styles from "./forgotpass.module.css";
import line from "../../../../public/line.svg";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { forgotPassApi } from "@/app/api/onboarding/forgotPassword";
import socialImg from "../../../../public/social.png";
require("dotenv").config();

const login = () => {
  const router = useRouter();

  const [email, setEmail] = useState("");

  const Data = {
    email: email,
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/password-reset/`;

  const onSubmit = async (e) => {
    e.preventDefault();

    try {
      const res = await forgotPass<PERSON>pi(URL, Data);

      if (res.status === 200) {
        toast.success(res.data.message);
      } else {
        toast.error("Login failed.");
      }
    } catch (error) {
      toast.error("Incorrect Email ID or Password");
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          <h1 className={styles.heading}>Reset Password Link Expired</h1>
          <div className={styles.subHeading}>
            Please generate a new reset password Link.
          </div>

          <div className={styles.loginBtnContainer}>
            <Link href="/sign/login" className={styles.loginBtn}>
              Redirect to login page
            </Link>
          </div>
          <ToastContainer />
        </div>
      </div>
      <div className={styles.rightContainer}>
        <div className={styles.rightBody}>
          <div className={styles.textContainer}>
            <div>
              <div className={styles.firstLine}>
                Please login to access the platform
              </div>
              <div className={styles.secondLine}>
                As a regulated market and for safety of users, we require
                accurate personal details to verify all parties to a transaction
              </div>
              <div className={styles.lastLine}>
                This should take just a few minutes to compleet in most cases
              </div>
            </div>
            {/* bottom section */}
            <div className={styles.bottomSection}>
              <div className={styles.btnCont}>
                <button className={styles.accessibilityBtn}>
                  Accesability
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                  >
                    <path
                      d="M6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72775 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C7.39239 0.75 8.72775 1.30312 9.71231 2.28769C10.6969 3.27226 11.25 4.60761 11.25 6C11.25 7.39239 10.6969 8.72775 9.71231 9.71231C8.72775 10.6969 7.39239 11.25 6 11.25ZM6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z"
                      fill="#407BFF"
                    />
                    <path
                      d="M6.944 5.03739L5.11196 5.22674L5.04636 5.47746L5.40637 5.53222C5.64157 5.5784 5.68797 5.64834 5.63677 5.84165L5.04636 8.12976C4.89116 8.72157 5.13036 9 5.69278 9C6.12878 9 6.63519 8.83374 6.8648 8.60545L6.9352 8.33099C6.7752 8.44711 6.54159 8.49329 6.38639 8.49329C6.16638 8.49329 6.08638 8.36596 6.14318 8.14163L6.944 5.03739ZM7 3.65978C7 3.83476 6.91571 4.00258 6.76568 4.12631C6.61565 4.25004 6.41216 4.31955 6.19998 4.31955C5.98781 4.31955 5.78432 4.25004 5.63429 4.12631C5.48426 4.00258 5.39997 3.83476 5.39997 3.65978C5.39997 3.48479 5.48426 3.31698 5.63429 3.19324C5.78432 3.06951 5.98781 3 6.19998 3C6.41216 3 6.61565 3.06951 6.76568 3.19324C6.91571 3.31698 7 3.48479 7 3.65978Z"
                      fill="#407BFF"
                    />
                  </svg>
                </button>
                <button className={styles.accessibilityBtn}>
                  Regulatory authority info
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="9"
                    height="11"
                    viewBox="0 0 9 11"
                    fill="none"
                  >
                    <path
                      d="M8.02083 8.625V9.875H0.729167V8.625H0V9.875C0 10.0408 0.0768227 10.1997 0.213568 10.3169C0.350313 10.4342 0.53578 10.5 0.729167 10.5H8.02083C8.21422 10.5 8.39969 10.4342 8.53643 10.3169C8.67318 10.1997 8.75 10.0408 8.75 9.875V8.625H8.02083Z"
                      fill="#407BFF"
                    />
                    <path
                      d="M8.125 4.93182L7.59625 4.41108L4.75 7.21051V0.5H4V7.21051L1.15375 4.41108L0.625 4.93182L4.375 8.625L8.125 4.93182Z"
                      fill="#407BFF"
                    />
                  </svg>
                </button>
              </div>
              <div className={styles.socialLinksCont}>
                <Image src={socialImg} alt="Picture of the author" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default login;
