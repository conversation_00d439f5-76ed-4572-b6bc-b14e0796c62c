/* Container and Base Styles */
.paymentContainer {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 16px;
  box-sizing: border-box;
}

.paymentCard {
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: box-shadow 0.2s ease;
  box-sizing: border-box;
}

.paymentCard:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

/* Header Styles */
.cardHeader {
  padding: 24px 24px 20px;
  border-bottom: 1px solid #F3F4F6;
}

.cardTitle {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
  letter-spacing: -0.025em;
}

.cardSubtitle {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #6B7280;
  margin: 0;
}

/* Payment Sections Container */
.paymentSections {
  padding: 24px;
}

/* Individual Payment Section */
.paymentSection {
  margin-bottom: 32px;
}

.paymentSection:last-child {
  margin-bottom: 0;
}

/* Section Header */
.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #E5E7EB;
}

.sectionLabel {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.selectedIndicator {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: #4F46E5;
  background: #EEF2FF;
  padding: 4px 12px;
  border-radius: 16px;
}

/* Section Divider */
.sectionDivider {
  height: 1px;
  background: #E5E7EB;
  margin: 32px 0;
}

/* Methods List */
.methodsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Method Item */
.methodItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #F9FAFB;
  border: 2px solid #E5E7EB;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  box-sizing: border-box;
  width: 100%;
  text-align: left;
}

.methodItem:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
  transform: translateX(4px);
}

.methodItemSelected {
  background: #EEF2FF;
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.methodItemSelected:hover {
  background: #EEF2FF;
  border-color: #4F46E5;
  transform: translateX(4px);
}

/* Method Content */
.methodContent {
  display: flex;
  align-items: center;
  gap: 16px;
}

.methodIcon {
  width: 40px;
  height: 40px;
  background: #FFFFFF;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.methodItemSelected .methodIcon {
  background: #4F46E5;
}

.methodInitial {
  font-size: 18px;
  font-weight: 600;
  color: #4F46E5;
}

.methodItemSelected .methodInitial {
  color: #FFFFFF;
}

.methodName {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.methodItemSelected .methodName {
  color: #1F2937;
  font-weight: 600;
}

/* Check Icon */
.checkIcon {
  width: 24px;
  height: 24px;
  background: #4F46E5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  flex-shrink: 0;
  animation: scaleIn 0.2s ease;
}

@keyframes scaleIn {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: #F9FAFB;
  border-radius: 12px;
  border: 2px dashed #E5E7EB;
}

.emptyIcon {
  color: #D1D5DB;
  margin-bottom: 12px;
}

.emptyText {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  color: #6B7280;
  max-width: 280px;
  line-height: 1.6;
  margin: 0;
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
  .paymentContainer {
    padding: 12px;
  }

  .cardHeader {
    padding: 20px 20px 16px;
  }

  .cardTitle {
    font-size: 18px;
  }

  .paymentSections {
    padding: 20px;
  }

  .sectionHeader {
    margin-bottom: 12px;
    padding-bottom: 10px;
  }

  .sectionLabel {
    font-size: 15px;
  }

  .selectedIndicator {
    font-size: 12px;
    padding: 3px 10px;
  }

  .methodItem {
    padding: 14px 16px;
  }

  .methodIcon {
    width: 36px;
    height: 36px;
  }

  .methodInitial {
    font-size: 16px;
  }

  .methodName {
    font-size: 13px;
  }

  .sectionDivider {
    margin: 24px 0;
  }
}

@media screen and (max-width: 480px) {
  .paymentContainer {
    padding: 8px;
  }

  .cardHeader {
    padding: 16px 16px 12px;
  }

  .cardTitle {
    font-size: 16px;
  }

  .cardSubtitle {
    font-size: 13px;
  }

  .paymentSections {
    padding: 16px;
  }

  .paymentSection {
    margin-bottom: 24px;
  }

  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .sectionLabel {
    font-size: 14px;
  }

  .selectedIndicator {
    font-size: 11px;
    padding: 2px 8px;
  }

  .methodsList {
    gap: 10px;
  }

  .methodItem {
    padding: 12px 14px;
  }

  .methodContent {
    gap: 12px;
  }

  .methodIcon {
    width: 32px;
    height: 32px;
  }

  .methodInitial {
    font-size: 14px;
  }

  .methodName {
    font-size: 12px;
  }

  .emptyState {
    padding: 32px 16px;
  }

  .emptyText {
    font-size: 13px;
  }

  .sectionDivider {
    margin: 20px 0;
  }
}

/* Accessibility and Focus States */
.methodItem:focus-visible {
  outline: 2px solid #4F46E5;
  outline-offset: 2px;
}

/* Button reset for better consistency */
.methodItem {
  font-family: inherit;
  border: 2px solid #E5E7EB;
}

/* Loading State (if needed) */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #F3F4F6;
  border-top-color: #4F46E5;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
