import axios from "axios";
require("dotenv").config();
const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
console.log("hello", BaseURL);
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const getDisputeListApi = async () => {
  const res = await axios({
    url: `${BaseURL}/get-dispute-list/`,
    method: "GET",
  });
  return res;
};
export const addDisputeListApi = async (Data) => {
  const res = await axios({
    url: `${BaseURL}/dispute-list/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};
export const getDisputesApi = async () => {
  const res = await axios({
    url: `${BaseURL}/dispute-list/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const getDisputesOrderDropDownApi = async () => {
  const res = await axios({
    url: `${BaseURL}/order-dispute-list/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
