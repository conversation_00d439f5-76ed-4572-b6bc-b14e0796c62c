/* Mobile-first responsive design for financial application */
.account_container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 8px;
}

.accountCards {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 320px;
    min-height: 280px;
    padding: 0;
    margin: 8px 0;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.accountCards:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
}

.accountCards::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Card Header */
.cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #f1f5f9;
}

.cardTypeSection {
    flex: 1;
}

.cardTypeLabel {
    color: #64748b;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.cardTypeValue {
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Action Buttons */
.actionButtons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.actionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0;
    position: relative;
    overflow: hidden;
}

.actionButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.actionButton:hover::before {
    opacity: 1;
}

.actionButton:active {
    transform: scale(0.95);
}

.editButton {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.editButton:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    transform: translateY(-1px);
}

.deleteButton {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.deleteButton:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    transform: translateY(-1px);
}

/* Card Content */
.cardContent {
    padding: 0 16px 12px;
    display: flex;
    flex-direction: column;
}

/* Horizontal Grid Layout */
.infoGrid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.infoGrid:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Horizontal Field Layout */
.horizontalField {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 4px 0;
    border-bottom: 1px solid #f1f5f9;
    min-height: 20px;
}

.horizontalField:last-child {
    border-bottom: none;
}

.fieldLabel {
    color: #6b7280;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
    min-width: 60px;
    max-width: 60px;
    text-align: left;
    line-height: 1.2;
    padding-top: 1px;
}

.fieldValue {
    color: #1f2937;
    font-size: 10px;
    font-weight: 500;
    line-height: 1.3;
    text-align: right;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    flex: 1;
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
}

/* Responsive Design - Tablet and Desktop */
@media screen and (min-width: 576px) {
    .account_container {
        padding: 3%;
    }

    .accountCards {
        width: 85%;
        max-width: 85%;
        min-width: 320px;
        min-height: 180px;
    }

    .cardHeader {
        padding: 16px 16px 12px;
    }

    .cardTypeValue {
        font-size: 16px;
    }

    .cardContent {
        padding: 0 16px 16px;
    }

    .infoGrid {
        padding: 10px;
        gap: 4px;
    }

    .fieldLabel {
        font-size: 10px;
        min-width: 65px;
        max-width: 65px;
    }

    .fieldValue {
        font-size: 11px;
    }

    .horizontalField {
        padding: 5px 0;
        min-height: 22px;
    }
}

/* 3-card horizontal layout for larger screens */
@media screen and (min-width: 768px) {
    .account_container {
        padding: 2%;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0;
    }

    .accountCards {
        width: 33.333%;
        max-width: 33.333%;
        min-width: 33.333%;
        min-height: 200px;
        margin: 0;
        flex: 0 0 33.333%;
    }

    .cardHeader {
        padding: 16px 14px 12px;
    }

    .cardTypeValue {
        font-size: 15px;
    }

    .actionButton {
        width: 32px;
        height: 32px;
    }

    .cardContent {
        padding: 0 14px 16px;
    }

    .infoGrid {
        padding: 8px;
        gap: 3px;
    }

    .fieldLabel {
        font-size: 9px;
        min-width: 55px;
        max-width: 55px;
    }

    .fieldValue {
        font-size: 10px;
    }

    .horizontalField {
        padding: 4px 0;
        min-height: 20px;
    }
}

@media screen and (min-width: 1024px) {
    .account_container {
        padding: 1.5%;
        gap: 0;
    }

    .accountCards {
        width: 33.333%;
        max-width: 33.333%;
        min-width: 33.333%;
        min-height: 220px;
        flex: 0 0 33.333%;
    }

    .cardHeader {
        padding: 18px 16px 14px;
    }

    .cardTypeValue {
        font-size: 16px;
    }

    .actionButton {
        width: 34px;
        height: 34px;
    }

    .cardContent {
        padding: 0 16px 18px;
    }

    .infoGrid {
        padding: 10px;
        gap: 3px;
    }

    .fieldLabel {
        font-size: 10px;
        min-width: 60px;
    }

    .fieldValue {
        font-size: 11px;
    }
}

@media screen and (min-width: 1200px) {
    .account_container {
        gap: 0;
        padding: 1%;
    }

    .accountCards {
        width: 33.333%;
        max-width: 33.333%;
        min-width: 33.333%;
        min-height: 240px;
        flex: 0 0 33.333%;
    }

    .cardHeader {
        padding: 20px 18px 16px;
    }

    .cardTypeValue {
        font-size: 18px;
    }

    .actionButton {
        width: 36px;
        height: 36px;
    }

    .cardContent {
        padding: 0 18px 20px;
    }

    .infoGrid {
        padding: 12px;
        gap: 4px;
    }

    .fieldLabel {
        font-size: 11px;
        min-width: 65px;
    }

    .fieldValue {
        font-size: 12px;
    }
}

@media screen and (min-width: 1440px) {
    .account_container {
        gap: 28px;
    }

    .accountCards {
        width: calc(33.333% - 19px);
        max-width: 420px;
        min-width: 350px;
        min-height: 260px;
    }

    .cardHeader {
        padding: 22px 20px 18px;
    }

    .cardTypeValue {
        font-size: 20px;
    }

    .actionButton {
        width: 38px;
        height: 38px;
    }

    .cardContent {
        padding: 0 20px 22px;
    }

    .infoGrid {
        padding: 14px;
        gap: 5px;
    }

    .fieldLabel {
        font-size: 12px;
        min-width: 70px;
    }

    .fieldValue {
        font-size: 13px;
    }
}

/* Accessibility and Focus States */
.actionButton:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

.editButton:focus {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5);
}

.deleteButton:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.5);
}

/* Loading and Animation States */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.accountCards.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .accountCards {
        border: 2px solid #000000;
        background: #ffffff;
    }

    .fieldValue {
        background: none;
        -webkit-background-clip: unset;
        -webkit-text-fill-color: unset;
        background-clip: unset;
        color: #000000;
    }

    .cardTypeValue {
        background: none;
        -webkit-background-clip: unset;
        -webkit-text-fill-color: unset;
        background-clip: unset;
        color: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .accountCards,
    .actionButton,
    .paymentMethodSection,
    .recipientSection,
    .businessSection,
    .additionalSection {
        transition: none;
    }

    .accountCards:hover {
        transform: none;
    }

    .actionButton:hover {
        transform: none;
    }

    .accountCards.loading {
        animation: none;
    }
}