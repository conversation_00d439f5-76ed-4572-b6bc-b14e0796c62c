.container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 20px 0px;
}

.Btncontainer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

.btn {
    background-color: black;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    margin: 10px 0px;
}

.btnContainer {
    width: 60%;
    display: flex;
    flex-direction: column;
    margin: auto;

    @media screen and (max-width: 576px) {
        width: 100%;
    }
}

.vidCont {
    width: 100%;
    margin: auto;
}

.container {
    width: 100%;
    margin: auto;
}