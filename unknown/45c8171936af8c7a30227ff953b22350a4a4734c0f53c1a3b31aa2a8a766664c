@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.main {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 103vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.wrapper {
  display: flex;
  width: 100%;
  height: 90vh;
  padding: 30px 10px;
  margin: auto;
  justify-content: space-between;
}

.leftContainerWrapper {
  width: 20%;
}

.leftContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 20px;
}

/* Logo area styles moved to SidebarHeader component */

.sidebarWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.sidebar {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  height: auto;
  max-height: calc(100vh - 160px);
  border-radius: 0 0 20px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-top: none;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(65, 83, 237, 0.3) transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @media screen and (max-width : 576px) {
    width: 100%;
    z-index: 99;
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
    border-radius: 0;
    border: none;
    overflow-y: auto;
    overflow-x: hidden;
    background: white;
  }
}

/* Custom scrollbar for webkit browsers */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

.sidebar:hover {
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

.pages {
  margin-top: 20px;
  width: 100%;
  height: auto;
  min-height: 100%;
  padding: 0 8px 60px 8px;
  display: flex;
  flex-direction: column;
}

.dashboardContainer {
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #475569;
  margin-bottom: 4px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.dashboardContainer::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  z-index: 0;
}

.dashboardContainer:hover::before {
  width: 4px;
}

.dashboardContainer:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
}

.historyContainer {
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #475569;
  margin-bottom: 4px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.historyContainer::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  z-index: 0;
}

.historyContainer:hover::before {
  width: 4px;
}

.historyContainer:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
}



.sideIcons {
  margin-left: 20px;
  margin-right: 16px;
  color: #64748b;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
}

.dashboardContainer:hover .sideIcons,
.historyContainer:hover .sideIcons {
  color: #667eea;
}

.sideIconsActive {
  color: #667eea;
}

.sideIcons1 {
  margin-left: 20px;
  margin-right: 16px;
  color: #667eea;
  position: relative;
  z-index: 1;
}

.dashboard {
  color: #475569;
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.dashboardContainer:hover .dashboard,
.historyContainer:hover .dashboard {
  color: #667eea;
  font-weight: 600;
}

.disabledForLaunch {
  color: #cbd5e1;
  cursor: not-allowed;
  text-decoration: none;
  font-weight: 400;
  font-size: 14px;
  opacity: 0.6;
  position: relative;
  z-index: 1;
}

.btnEnabled {
  text-decoration: none;
  color: #475569;
  font-weight: 500;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.btnEnabled:hover {
  color: #667eea;
  font-weight: 600;
}

.btnActive {
  text-decoration: none;
  color: #667eea;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

/* Active state styling for container */
.dashboardContainer:has(.btnActive) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.12), rgba(118, 75, 162, 0.12));
}

.dashboardContainer:has(.btnActive)::before {
  width: 4px;
}

.dashboardContainer:has(.btnActive) .sideIcons {
  color: #667eea;
}

.activeLink {
  text-decoration: none;
}

.activeLink:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
  border-radius: 8px;
}

.activeLink1 {
  text-decoration: none;
}

.activeLink1:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
  border-radius: 8px;
}

.rightContainer {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0.5px solid black;
  height: 100vh;
}

.rightContainerWrapper {
  width: 78%;
}

/* Enhanced animations removed to prevent ripple effect */

/* Responsive improvements */
@media screen and (max-width: 768px) {
  .sidebar {
    border-radius: 0 0 15px 15px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-top: none;
  }
  
  .dashboardContainer,
  .historyContainer {
    height: 52px;
  }
  
  .sideIcons {
    margin-left: 16px;
    margin-right: 12px;
  }
  
  .dashboard,
  .btnEnabled,
  .btnActive {
    font-size: 13px;
  }
}

/* Mobile - when header is hidden, sidebar gets full radius */
@media screen and (max-width: 576px) {
  .sidebarWrapper {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .sidebar {
    border-radius: 0;
    border: none;
    flex: 1;
    min-height: 0;
    background: white;
  }

  .pages {
    /* padding: 20px 20px 80px 20px; */
    overflow-y: auto;
    flex: 1;
  }
}