"use client";
import { createContext, useContext, useEffect, useRef, useState } from 'react';

import { usePathname } from 'next/navigation';
import { toast } from 'react-toastify';
import refreshTokenApi from '../api/onboarding/refreshTokenEndpoint';
import { customFetchWithToken } from '../utils/axiosInterpreter';

const SSEContext = createContext(undefined);

export const SSEProvider = ({ children }) => {
    const [event, setEvent] = useState("");
    const [remflowEvent, setRemflowEvent] = useState("");
    const eventSourceRef = useRef(null);
    const remflowEventSourceRef = useRef(null);
    const retryTimeoutRef = useRef(null);
    const remflowRetryTimeoutRef = useRef(null);
    const pathname = usePathname();
    const sseErrorCountRef = useRef(0);
    const remflowSseErrorCountRef = useRef(0);
    const lastToastMessageRef = useRef(null);
    const lastToastTimeRef = useRef(0);
    const lastTradeRequestIdRef = useRef(null); // Global state for duplicate prevention
    const maxRetries = 5;
    const baseRetryDelay = 5000; // 5 seconds
    // console.log("event12", event);
    // console.log("remflowEvent", remflowEvent);


   

    // Get token from sessionStorage like other parts of the app
    const getToken = () => {
        if (typeof window !== "undefined") {
            return sessionStorage.getItem("user");
        }
        return null;
    };

    const refreshApiCall = async () => {
        try {
            const res = await refreshTokenApi();
            if (typeof window !== "undefined") {
                sessionStorage.setItem("user", res.data.data.access_token);
                localStorage.setItem("user", res.data.data.access_token);
            }
            return res;
        } catch (error) {
            console.error("Error refreshing token:", error);
            throw error;
        }
    };

    const scheduleRetry = (delayMultiplier = 1, isRemflow = false) => {
        const delay = baseRetryDelay * delayMultiplier;
        console.log(`Scheduling ${isRemflow ? 'Remflow' : 'Notification'} SSE retry in ${delay}ms`);
        
        const timeoutRef = isRemflow ? remflowRetryTimeoutRef : retryTimeoutRef;
        timeoutRef.current = setTimeout(() => {
            const errorCount = isRemflow ? remflowSseErrorCountRef.current : sseErrorCountRef.current;
            if (errorCount < maxRetries) {
                console.log(`Retrying ${isRemflow ? 'Remflow' : 'Notification'} SSE connection (attempt ${errorCount + 1}/${maxRetries})`);
                if (isRemflow) {
                    setupRemflowSSEConnection();
                } else {
                    setupNotificationSSEConnection();
                }
            } else {
                console.log(`Max ${isRemflow ? 'Remflow' : 'Notification'} SSE retry attempts reached`);
            }
        }, delay);
    };

    const setupNotificationSSEConnection = () => {
        const token = getToken();
        
        if (!token) {
            console.log("No token found for notification SSE, will retry in 5 seconds");
            scheduleRetry(1, false);
            return;
        }

        // Don't connect on login/register pages
        if (pathname === "/sign/login" || pathname === "/") {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
                eventSourceRef.current = null;
            }
            return;
        }

        try {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
            }

            // Clear any existing retry timeout
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
                retryTimeoutRef.current = null;
            }

            // Create URL with token in query parameter for notifications
            // Note: EventSource doesn't support custom headers, so we pass token as query param
            const sseUrl = new URL(`${process.env.NEXT_PUBLIC_Base_URL}/notification-sse/`);
            sseUrl.searchParams.append('token', token);

            const eventSource = new EventSource(sseUrl.toString());

            eventSource.onopen = () => {
                console.log("Notification SSE connection opened");
                sseErrorCountRef.current = 0; // Reset error count on successful connection
            };

            eventSource.addEventListener("notification", (event) => {
                const notifData = JSON.parse(event.data);
                console.log("notifData", notifData);
                if (notifData) {
                    // Handle toast for trade requests globally to prevent duplicates
                    if (notifData.type === "trade_request" && notifData.id !== lastTradeRequestIdRef.current) {
                        toast.success(notifData.notifiaction_msg, {
                            toastId: `trade_${notifData.id}`,
                            position: "top-right",
                            autoClose: 5000,
                            hideProgressBar: false,
                            closeOnClick: true,
                            pauseOnHover: true,
                            draggable: true
                        });
                        lastTradeRequestIdRef.current = notifData.id;
                    }
                    
                    setEvent(notifData);
                }
            });

            // Add more specific error logging
            eventSource.onerror = async (event) => {
                console.error("Notification SSE Error:", event);
                eventSource.close();
                
                if (sseErrorCountRef.current < maxRetries) {
                    sseErrorCountRef.current++;
                    try {
                        await refreshApiCall();
                        // Use exponential backoff: 1x, 2x, 3x delay etc.
                        scheduleRetry(sseErrorCountRef.current, false);
                    } catch (error) {
                        console.error("Failed to refresh token for notification SSE:", error);
                        scheduleRetry(sseErrorCountRef.current, false);
                    }
                } else {
                    console.error("Max notification SSE reconnection attempts reached");
                }
            };

            eventSourceRef.current = eventSource;

        } catch (error) {
            console.error("Error setting up notification SSE:", error);
            if (error?.response?.status === 401) {
                refreshApiCall().then(() => {
                    scheduleRetry(1, false);
                }).catch(() => {
                    scheduleRetry(1, false);
                });
            } else {
                scheduleRetry(1, false);
            }
        }
    };

    const setupRemflowSSEConnection = () => {
        const token = getToken();
        
        if (!token) {
            console.log("No token found for remflow SSE, will retry in 5 seconds");
            scheduleRetry(1, true);
            return;
        }

        // Don't connect on login/register pages
        if (pathname === "/sign/login" || pathname === "/") {
            if (remflowEventSourceRef.current) {
                remflowEventSourceRef.current.close();
                remflowEventSourceRef.current = null;
            }
            return;
        }

        try {
            if (remflowEventSourceRef.current) {
                remflowEventSourceRef.current.close();
            }

            // Clear any existing retry timeout
            if (remflowRetryTimeoutRef.current) {
                clearTimeout(remflowRetryTimeoutRef.current);
                remflowRetryTimeoutRef.current = null;
            }

            // Create URL with token in query parameter for remflow SSE
            const remflowSseUrl = new URL(`${process.env.NEXT_PUBLIC_Base_URL}/sse/connect/?token=${token}`);
         

            const remflowEventSource = new EventSource(remflowSseUrl.toString());

            remflowEventSource.onopen = () => {
                console.log("Remflow SSE connection opened");
                remflowSseErrorCountRef.current = 0; // Reset error count on successful connection
            };

            remflowEventSource.addEventListener("trade", (event) => {
                try {
                    const eventData = JSON.parse(event.data);
                    console.log("Remflow SSE data:", eventData);
                    setRemflowEvent(eventData);

                    // Handle different types of remflow events with duplicate prevention
                    if (eventData.message) {
                        // Create a unique ID based on the event data
                        const eventId = eventData.trade_id || eventData.id || eventData.message;
                        const toastId = `trade-${eventId}`;
                        
                        // Only show toast if it hasn't been shown before
                        if (!toast.isActive(toastId)) {
                            toast.success(eventData.message, {
                                toastId,
                                position: "top-right",
                                autoClose: 3000,
                                hideProgressBar: false,
                                pauseOnHover: true,
                                draggable: true,
                                closeOnClick: true
                            });
                        }
                    }
                } catch (error) {
                    console.error("Error parsing remflow SSE data:", error);
                    console.log("Raw remflow event data:", event.data);
                    setRemflowEvent(event.data);
                }
            });

            // Add more specific error logging
            remflowEventSource.onerror = async (event) => {
                console.error("Remflow SSE Error:", event);
                remflowEventSource.close();
                
                if (remflowSseErrorCountRef.current < maxRetries) {
                    remflowSseErrorCountRef.current++;
                    try {
                        await refreshApiCall();
                        // Use exponential backoff: 1x, 2x, 3x delay etc.
                        scheduleRetry(remflowSseErrorCountRef.current, true);
                    } catch (error) {
                        console.error("Failed to refresh token for remflow SSE:", error);
                        scheduleRetry(remflowSseErrorCountRef.current, true);
                    }
                } else {
                    console.error("Max remflow SSE reconnection attempts reached");
                }
            };

            remflowEventSourceRef.current = remflowEventSource;

        } catch (error) {
            console.error("Error setting up remflow SSE:", error);
            if (error?.response?.status === 401) {
                refreshApiCall().then(() => {
                    scheduleRetry(1, true);
                }).catch(() => {
                    scheduleRetry(1, true);
                });
            } else {
                scheduleRetry(1, true);
            }
        }
    };

    useEffect(() => {
        const token = getToken();
        if (token && pathname !== "/sign/login" && pathname !== "/") {
            // Reset error counts when pathname changes or component mounts
            sseErrorCountRef.current = 0;
            remflowSseErrorCountRef.current = 0;
            
            // Setup both SSE connections
            setupNotificationSSEConnection();
            setupRemflowSSEConnection();
        }

        return () => {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
                eventSourceRef.current = null;
            }
            if (remflowEventSourceRef.current) {
                remflowEventSourceRef.current.close();
                remflowEventSourceRef.current = null;
            }
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
                retryTimeoutRef.current = null;
            }
            if (remflowRetryTimeoutRef.current) {
                clearTimeout(remflowRetryTimeoutRef.current);
                remflowRetryTimeoutRef.current = null;
            }
        };
    }, [pathname]); // Add pathname as dependency to reconnect on route changes

    return (
        <SSEContext.Provider value={{event, remflowEvent, lastTradeRequestId: lastTradeRequestIdRef.current}}>
            {children}
        </SSEContext.Provider>
    );
};

export const useSSE = () => {
    const context = useContext(SSEContext);
    if (context === undefined) {
        throw new Error('useSSE must be used within a SSEProvider');
    }
    return context;
};