"use client";
import { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";
import { customFetchWithToken } from "../utils/axiosInterpreter";
import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
const TimerContext = createContext();

const MODAL_TRIGGER_TIME = 14 * 60; // 14 minutes in seconds

const TIMER_KEY = "timer_start_time";
const SESSION_REFRESH_KEY = "session_refresh_allowed";

export const TimerContextProvider = ({ children }) => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const [sessionRefreshAllowed, setSessionRefreshAllowed] = useState(true);
  const [startTime, setStartTime] = useState(null);
  const [isLogout, setIsLogout] = useState(false);

  // Initialize timer state
  useEffect(() => {
    const initializeTimer = () => {
      const savedTime = localStorage.getItem(TIMER_KEY);
      const userToken = sessionStorage.getItem("user");

      if (userToken && savedTime) {
        setStartTime(parseInt(savedTime));
        checkElapsedTime(parseInt(savedTime));
      } else {
        resetTimerState();
      }
    };

    if (typeof window !== "undefined") {
      initializeTimer();
    }
  }, []);

  const checkElapsedTime = (start) => {
    const elapsedSeconds = Math.floor((Date.now() - start) / 1000);

    if (
      elapsedSeconds >= MODAL_TRIGGER_TIME &&
      elapsedSeconds <= MODAL_TRIGGER_TIME + 60
    ) {
      setShowModal(true);
    } else if (elapsedSeconds > MODAL_TRIGGER_TIME + 60) {
      stopTimer();
      setShowModal(false);
    }
  };

  // const handleAutoLogout = () => {
  //   toast.info("Session expired. Please log in again.");
  //   stopTimer();
  // };

  // Timer interval management
  useEffect(() => {
    let intervalId;

    if (startTime && !isLogout) {
      // Only start interval if not logged out
      intervalId = setInterval(() => {
        const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);

        if (elapsedSeconds >= MODAL_TRIGGER_TIME && !showModal) {
          setShowModal(true);
        }

        if (elapsedSeconds > MODAL_TRIGGER_TIME + 60) {
          stopTimer();
        }
      }, 1000);
    }

    return () => {
      if (intervalId) clearInterval(intervalId); // Always clean up if there's an interval
    };
  }, [startTime, showModal, isLogout]); // Add isLogout to dependencies

  const startTimer = () => {
    const newStart = Date.now();
    localStorage.setItem(TIMER_KEY, newStart.toString());
    localStorage.setItem(SESSION_REFRESH_KEY, "true");
    setStartTime(newStart);
    setShowModal(false); // Force modal closure on new timer
    setSessionRefreshAllowed(true);
  };

  const stopTimer = () => {
    toast.info("Session expired. Please log in again.");
    setIsLogout(true);

    setTimeout(() => {
      if (typeof window !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
      }
      if (typeof window !== "undefined") {
        window.location.href = "/sign/login";
      }
    }, 1500);
  };

  const handleSessionContinue = async () => {
    try {
      if (window !== "undefined") {
        let refreshToken = localStorage.getItem("refreshToken");
        const response = await axios({
          url: `${Base_url}/get-access-token/?refresh_token=${refreshToken}`,
          method: "GET",
        });
        sessionStorage.setItem("user", response.data.data.access_token);
        startTimer();
        toast.success("Session refreshed!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Could not refresh session.");
    }
  };

  const resetTimerState = () => {
    setStartTime(null);
    setShowModal(false);
    setSessionRefreshAllowed(false);
  };

  return (
    <TimerContext.Provider
      value={{
        showModal,
        sessionRefreshAllowed,
        handleSessionContinue,
        handleSessionEnd: stopTimer,
        startTimer,
        stopTimer,
      }}
    >
      {children}
    </TimerContext.Provider>
  );
};

export const useTimer = () => {
  const context = useContext(TimerContext);
  if (!context) {
    throw new Error("useTimer must be used within TimerContextProvider");
  }
  return context;
};
