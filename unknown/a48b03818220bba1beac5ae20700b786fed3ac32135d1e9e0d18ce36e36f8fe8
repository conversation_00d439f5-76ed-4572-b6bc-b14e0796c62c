"use client";
import { useEffect, useState, useRef } from "react";
import styles from "./dispute.module.css";
import { getDisputeListApi } from "@/app/api/disputeApi's/dispute";
import DisputeDropdown from "../../components/DisputeDropdown/page";
import Layout from "../../components/Layout/page";
import Login from "@/app/sign/login/page";
import DisputeCard from "../../components/DisputeCard/page";
import { getDisputesApi } from "@/app/api/disputeApi's/dispute";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = () => {
  const [showDisputes, setShowDisputes] = useState(true);
  const [showAddDisputes, setShowAddDisputes] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);
  const [selectedEvidence, setSelectedEvidence] = useState(null);
  const authTokenRef = useRef(null);
  const [selectedOption, setSelectedOption] = useState(-1);

  const [loadedDisputesList, setLoadedDisputesList] = useState([]);
  const [loadedDisputesArr, setLoadedDisputesArr] = useState([]);
  const [loading, setLoading] = useState(false);

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const showAddDisputesHandler = () => {
    setShowDisputes(false);
    setShowAddDisputes(true);
  };
  const showDisputesHandler = () => {
    setShowDisputes(true);
    setShowAddDisputes(false);
  };

  const fetchAvailableDisputes = async () => {
    setLoading(true);
    try {
      const res = await customFetchWithToken.get("/dispute-list/");

      setLoadedDisputesArr(res.data.data);
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message, { toastId: "error-toast" });
    } finally {
      setLoading(false);
    }
  };

  const fetchDisputeReason = async () => {
    try {
      const response = await customFetchWithToken.get("/get-dispute-list/");
      setLoadedDisputesList(response.data.data);
    } catch (err) {
      console.error(err);
    }
  };

  const handleSelectedOption = (e) => {
    // Get the value from the button element
    const value = e.currentTarget.value;
    setSelectedOption(parseInt(value));
  };

  useEffect(() => {
    fetchDisputeReason();
  }, []);
  useEffect(() => {
    fetchAvailableDisputes();
  }, []);

  const disputeTitle = (
    <div className={styles.desktopHeaderContent}>
      <h1 className={styles.pageTitle}>Dispute Management</h1>
      <p className={styles.pageSubtitle}>
        Manage and resolve trading disputes
      </p>
    </div>
  );

  return (
    <>
      <div className={styles.pageContainer}>
        <Layout title={disputeTitle}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.headerSection}>
            <div className={styles.mobileHeaderContent}>
              <h1 className={styles.pageTitle}>Dispute Management</h1>
              <p className={styles.pageSubtitle}>
                Manage and resolve trading disputes
              </p>
            </div>
            <div className={styles.actionButtons}>
              <button
                className={`${styles.actionBtn} ${showDisputes ? styles.activeBtn : styles.secondaryBtn}`}
                onClick={showDisputesHandler}
                aria-pressed={showDisputes}
              >
                <svg className={styles.btnIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                View Disputes
              </button>
              <button
                className={`${styles.actionBtn} ${showAddDisputes ? styles.activeBtn : styles.primaryBtn}`}
                onClick={showAddDisputesHandler}
                aria-pressed={showAddDisputes}
              >
                <svg className={styles.btnIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Create Dispute
              </button>
            </div>
          </div>

          {/* Main Content */}
          <main className={styles.mainContent}>
            {loading ? (
              <div className={styles.loadingContainer}>
                <div className={styles.loadingSpinner}>
                  <div className={styles.spinner}></div>
                </div>
                <p className={styles.loadingText}>Loading disputes...</p>
              </div>
            ) : (
              <div className={styles.contentWrapper}>
                {showAddDisputes ? (
                  <div className={styles.createDisputeSection}>
                    <div className={styles.formCard}>
                      <div className={styles.formHeader}>
                        <h2 className={styles.formTitle}>Create New Dispute</h2>
                        <p className={styles.formDescription}>
                          Select a reason for your dispute and provide necessary details
                        </p>
                      </div>

                      <div className={styles.formContent}>
                        <div className={styles.fieldGroup}>
                          <label className={styles.fieldLabel}>Dispute Reason</label>
                          <div className={styles.reasonGrid}>
                            {loadedDisputesList.map((el) => (
                              <button
                                key={el.id}
                                className={`${styles.reasonBtn} ${selectedOption == el.id ? styles.selectedReason : ''}`}
                                value={el.id}
                                onClick={handleSelectedOption}
                                type="button"
                              >
                                <span className={styles.reasonText}>{el.disput_title}</span>
                                {selectedOption == el.id && (
                                  <svg className={styles.checkIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                )}
                              </button>
                            ))}
                          </div>
                        </div>

                        {/* Conditional Dispute Forms */}
                        {selectedOption == 1 && (
                          <div className={styles.disputeForm}>
                            <DisputeDropdown
                              title="Funds not received on time"
                              selectedId={selectedOption}
                            />
                          </div>
                        )}

                        {selectedOption == 2 && (
                          <div className={styles.disputeForm}>
                            <DisputeDropdown
                              title="Third Party payment"
                              selectedId={selectedOption}
                            />
                          </div>
                        )}

                        {selectedOption == 3 && (
                          <div className={styles.disputeForm}>
                            <DisputeDropdown
                              title="Adjust amount (underpaid)"
                              selectedId={selectedOption}
                            />
                          </div>
                        )}

                        {selectedOption == 4 && (
                          <div className={styles.disputeForm}>
                            <DisputeDropdown
                              title="Suspicious behaviour"
                              selectedId={selectedOption}
                            />
                          </div>
                        )}

                        {selectedOption == 5 && (
                          <div className={styles.disputeForm}>
                            <div className={styles.fieldGroup}>
                              <label htmlFor="otherReason" className={styles.fieldLabel}>
                                Describe your issue
                              </label>
                              <textarea
                                id="otherReason"
                                className={styles.textArea}
                                placeholder="Please provide detailed information about your dispute..."
                                maxLength="200"
                                rows="6"
                              ></textarea>
                              <div className={styles.charCount}>0/200 characters</div>
                            </div>
                            <button className={styles.submitBtn} type="submit">
                              Submit Dispute
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className={styles.disputeListSection}>
                    {loadedDisputesArr?.length > 0 ? (
                      <>
                        <div className={styles.listHeader}>
                          <h2 className={styles.listTitle}>Your Disputes</h2>
                          <div className={styles.disputeCount}>
                            {loadedDisputesArr.length} dispute{loadedDisputesArr.length !== 1 ? 's' : ''}
                          </div>
                        </div>

                        {/* Table View for Desktop */}
                        <div className={styles.tableContainer}>
                          <div className={styles.tableWrapper}>
                            <table className={styles.disputeTable}>
                              <thead className={styles.tableHeader}>
                                <tr>
                                  <th className={styles.tableHeaderCell}>
                                    <div className={styles.headerContent}>
                                      <svg className={styles.headerIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                      </svg>
                                      Dispute Type
                                    </div>
                                  </th>
                                  <th className={styles.tableHeaderCell}>
                                    <div className={styles.headerContent}>
                                      <svg className={styles.headerIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                                      </svg>
                                      Order Number
                                    </div>
                                  </th>
                                  <th className={styles.tableHeaderCell}>
                                    <div className={styles.headerContent}>
                                      <svg className={styles.headerIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                      </svg>
                                      Evidence
                                    </div>
                                  </th>
                                  <th className={styles.tableHeaderCell}>
                                    <div className={styles.headerContent}>
                                      <svg className={styles.headerIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                      </svg>
                                      Comment
                                    </div>
                                  </th>
                                  <th className={styles.tableHeaderCell}>
                                    <div className={styles.headerContent}>
                                      <svg className={styles.headerIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V7a2 2 0 012-2h2a2 2 0 012 2v0M8 7v8a2 2 0 002 2h4a2 2 0 002-2V7M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2" />
                                      </svg>
                                      Created Date
                                    </div>
                                  </th>
                                </tr>
                              </thead>
                              <tbody className={styles.tableBody}>
                                {loadedDisputesArr.map((el, index) => (
                                  <DisputeCard
                                    key={index}
                                    title={el.disput_title__disput_title}
                                    orderNo={el.order_id__order_number}
                                    evidence={el.upload_evidence}
                                    comment={el.comments}
                                    createdDate={el.created_date}
                                    index={index}
                                  />
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        {/* Mobile Card View */}
                        <div className={styles.mobileCardContainer}>
                          {loadedDisputesArr.map((el, index) => (
                            <div key={index} className={styles.mobileCard}>
                              <div className={styles.mobileCardHeader}>
                                <div className={styles.mobileStatusBadge}>
                                  <svg className={styles.mobileStatusIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                  </svg>
                                  Active Dispute
                                </div>
                                <div className={styles.mobileOrderNumber}>#{el.order_id__order_number || 'N/A'}</div>
                              </div>

                              <div className={styles.mobileCardContent}>
                                <h3 className={styles.mobileDisputeTitle}>{el.disput_title__disput_title || 'Untitled Dispute'}</h3>

                                {el.upload_evidence && (
                                  <div className={styles.mobileEvidenceSection}>
                                    <button 
                                      onClick={() => {
                                        setSelectedEvidence(el.upload_evidence);
                                        setShowEvidenceModal(true);
                                      }}
                                      className={styles.mobileEvidenceLink}
                                    >
                                      <span className={styles.mobileEvidenceLabel}>View Evidence</span>
                                      <svg className={styles.mobileEvidenceIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                      </svg>
                                    </button>
                                  </div>
                                )}

                                {el.comments && (
                                  <div className={styles.mobileCommentSection}>
                                    <p className={styles.mobileComment}>
                                      {el.comments.length > 100 ? `${el.comments.substring(0, 100)}...` : el.comments}
                                    </p>
                                  </div>
                                )}
                              </div>

                              <div className={styles.mobileCardFooter}>
                                <div className={styles.mobileCreatedDate}>
                                  <svg className={styles.mobileDateIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V7a2 2 0 012-2h2a2 2 0 012 2v0M8 7v8a2 2 0 002 2h4a2 2 0 002-2V7M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2" />
                                  </svg>
                                  <span>
                                    {el.created_date ? new Date(el.created_date).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'short',
                                      day: 'numeric',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    }) : 'Date not available'}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className={styles.emptyState}>
                        <div className={styles.emptyIcon}>
                          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h3 className={styles.emptyTitle}>No Disputes Found</h3>
                        <p className={styles.emptyDescription}>
                          You haven't created any disputes yet. Click "Create Dispute" to get started.
                        </p>
                        <button
                          className={styles.emptyActionBtn}
                          onClick={showAddDisputesHandler}
                        >
                          Create Your First Dispute
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </main>
        </Layout>
      </div>
      
      {/* Evidence Modal */}
      {showEvidenceModal && selectedEvidence && (
        <div className={styles.modalOverlay} onClick={() => setShowEvidenceModal(false)}>
          <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
            <div className={styles.modalHeader}>
              <h3>Evidence</h3>
              <button 
                className={styles.closeButton}
                onClick={() => setShowEvidenceModal(false)}
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className={styles.modalBody}>
              <img 
                src={selectedEvidence} 
                alt="Dispute Evidence" 
                className={styles.evidenceImage}
              />
            </div>
          </div>
        </div>
      )}
      
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </>
  );
};

export default page;
