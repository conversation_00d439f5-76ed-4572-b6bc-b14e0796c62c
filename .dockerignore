# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js build output
.next
out

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env*.local
.env.sentry-build-plugin

# Version control
.git
.gitignore

# IDE specific files
.idea
.vscode
*.swp
*.swo

# Debug files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage
.nyc_output

# System Files
.DS_Store
Thumbs.db

# Sentry
.sentryclirc
*.sentryclirc

# Misc
README.md
*.log
.aider*