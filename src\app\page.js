"use client";
import { useState, useRef } from "react";
import Image from "next/image";
import styles from "./sign/register/register.module.css";
import remflow from "../../public/rem_logo.png";
import line from "../../public/line.svg";
import RightSideAnimation from "./components/RightSideAnimation/RightSideAnimation";
import { toast, ToastContainer } from "react-toastify";
import { useRouter, useSearchParams } from "next/navigation";

import "react-toastify/dist/ReactToastify.css";
import registerApi from "./api/onboarding/register";
import ReCaptcha from "./components/ReCaptcha/page";
import verifyCaptcha from "@/app/api/onboarding/verifycaptcha";
import { verifyRefCode } from "./api/referalCodeApi/referalApi";
import ConversionTracker from "./components/ConversionTracker";

require("dotenv").config();

const register = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [firstname, setFirstName] = useState("");
  const [lastname, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [refCode, setRefCode] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [passDialogue, setPassDialogue] = useState("");
  const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
  const recaptchaRef = useRef();
  const [captchaToken, setCaptchatoken] = useState(null);
  const [btnIsActive, setBtnIsActive] = useState(false);
  const verifyRefCodeHandler = async () => {
    try {
      const res = await verifyRefCode();

      // toast.success(res.data.message);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCaptchaChange = (value) => {
    setIsCaptchaVerified(!!value);
    setCaptchatoken(value);
  };

  const handlePassword = (e) => {
    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    if (!passwordRegex.test(password)) {
      setPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      // Password does not match the criteria
      setBtnIsActive(false); // Reset button state so the form can be submitted again
    } else if (passwordRegex.test(password)) {
      setPassDialogue("");
    }
    setPassword(e.target.value);
  };

  function encodeData(data) {
    return btoa(data); // Base64 encoding
  }
  //  encoding the email and names
  const encodedEmail = encodeData(email);
  const encodedFirstName = encodeData(firstname);
  const encodedLastName = encodeData(lastname);

  const Data = {
    email: email,
    firstname: firstname,
    lastname: lastname,
    password: password,
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
  const isReferal = searchParams.get("referral");

  const URL = `${BaseURL}/register/`;
  // const URL = `${BaseURL}/register/${referral ? `?referral=${referral}` : ""}`;
  const URL1 = `${BaseURL}/register/${
    isReferal ? `?referral=${isReferal}` : ""
  }`;
  const CaptchaURL = `${BaseURL}/verifycaptch/`;
  const onSubmit = async (e) => {
    e.preventDefault();
    setPassDialogue("");
    setBtnIsActive(true);

    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    if (!passwordRegex.test(password)) {
      setPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      // Password does not match the criteria
      setBtnIsActive(false); // Reset button state so the form can be submitted again
      return;
    }

    // Password is valid, continue with further logic

    try {
      // Verify captcha
      const verify = await verifyCaptcha(CaptchaURL, captchaToken);
      if (verify?.data?.success !== true) {
        toast.error("Captcha Not Verified Successfully");
        recaptchaRef?.current?.reset();
        setBtnIsActive(false);
        return;
      }

      // Attempt registration
      const res = await registerApi(URL1, Data);
      if (res.status === 200 || res.status === 201) {
        toast.success("Registration successful!");

        // Store necessary information in local and session storage
        const { user_id, tokens, flag } = res.data.data;
        localStorage.setItem("userID", user_id);
        localStorage.setItem("user", tokens.access);
        localStorage.setItem("refreshToken", tokens.refresh);
        localStorage.setItem("verificationStatus", flag);
        sessionStorage.setItem("user", tokens.access);

        // Redirect after a delay
        setTimeout(() => {
          router.push("/pages/survey");
        }, 1500);
      } else {
        toast.error("Registration failed.");
        recaptchaRef?.current?.reset();
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Please refresh the page and try again"
      );
      recaptchaRef?.current?.reset();
      console.log("Error:", error);
    } finally {
      setBtnIsActive(false);
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>
            <Image
              src={remflow}
              alt="Picture of the logo"
              width={250}
              height={100}
            />
          </div>
          <div className={styles.heading}>Register</div>
          <ConversionTracker />
        

       
          <form action="" onSubmit={onSubmit}>
            <div className={styles.name}>
              <label className={styles.nameLabel} htmlFor="firstname">
                First Name
              </label>
              <input
                type="text"
                id="firstname"
                maxLength={260}
                value={firstname}
                onChange={(e) => setFirstName(e.target.value)}
                required
                autoComplete="off"
              />
            </div>
            <div className={styles.name}>
              <label className={styles.nameLabel} htmlFor="lastname">
                Last Name
              </label>
              <input
                type="text"
                id="lastname"
                maxLength={260}
                value={lastname}
                onChange={(e) => setLastName(e.target.value)}
                required
                autoComplete="off"
              />
            </div>
            <div className={styles.name}>
              <label className={styles.nameLabel} htmlFor="email">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                maxLength={260}
                onChange={(e) => setEmail(e.target.value)}
                required
                // autoComplete="off"
              />
            </div>
            <div className={styles.name}>
              <label className={styles.nameLabel} htmlFor="password">
                password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                maxLength={260}
                onChange={handlePassword}
                required
                // autoComplete="off"
              />
              <span
                className={styles.hidePass}
                onClick={() => setShowPassword(!showPassword)}
              >
                  {!showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
            </div>
            <div className={styles.wrongPassDialogue}>
              {passDialogue || "\u00A0"}
            </div>

            <div className={styles.captchaCont}>
              <ReCaptcha
                onChange={handleCaptchaChange}
                recaptchaRef={recaptchaRef}
              />
            </div>
            <div className={styles.terms}>
              <div>
                <input type="checkbox" id="agreeTerms" required />
                <label htmlFor="agreeTerms">
                  I agree to the terms & Privacy conditions
                </label>
              </div>
            </div>

            <div className={styles.loginBtnContainer}>
              <button
                type="submit"
                className={styles.registerBtn}
                disabled={btnIsActive}
              >
                Register
              </button>
            </div>
          </form>
          <ToastContainer />
          <div className={styles.lastPart}>
            You have an account ?
            <a href="/sign/login" className={styles.customLink}>
              <div className={styles.loginBtn}>Login</div>
            </a>
          </div>
        </div>
      </div>
      <RightSideAnimation />
    </main>
  );
};

export default register;
