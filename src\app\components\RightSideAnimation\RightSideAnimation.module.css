/* RIGHT CONTAINER */
.rightContainer {
  width: 45%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  @media screen and (max-width: 900px) {
    display: none;
  }
}

.rightBody {
  width: 95%;
  height: 95%;
  border-radius: 16px;
  background: linear-gradient(135deg, #4153ed 0%, #3d4ed8 50%, #2563eb 100%);
  position: relative;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;

  @media screen and (max-width: 900px) {
    width: 100%;
    height: auto;
    min-height: 400px;
    border-radius: 12px;
    margin: 0 auto;
  }
}

.rightBody::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)" /></svg>') repeat;
  pointer-events: none;
}

.rightBody::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.textContainer {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  width: 80%;
  height: 100%;
  margin-left: 60px;
  padding: 60px 0;
  position: relative;
  z-index: 2;
}

.firstLine {
  font-size: 32px;
  font-weight: 700;
  font-family: poppins;
  color: #fff;
  margin: 0 0 24px 0;
  line-height: 1.2;
  letter-spacing: -0.5px;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.secondLine {
  font-size: 18px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 20px 0;
  padding-right: 20px;
  font-family: poppins;
  line-height: 1.6;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.lastLine {
  font-size: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  font-family: poppins;
  margin: 0 0 40px 0;
  padding-right: 20px;
  line-height: 1.5;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

/* Feature highlights for fintech */
.featureHighlights {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 20px 0 40px 0;
  padding-right: 40px;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-family: poppins;
  font-size: 14px;
  font-weight: 500;
}

.featureIcon {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.featureIcon svg {
  width: 12px;
  height: 12px;
  fill: #fff;
}

/* Security badge */
.securityBadge {
  position: absolute;
  top: 40px;
  right: 40px;
  background: rgba(34, 197, 94, 0.15);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 24px;
  padding: 8px 16px;
  color: #22c55e;
  font-family: poppins;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 6px;
}

.securityBadge::before {
  content: '🔒';
  font-size: 14px;
}

/* Stats or trust indicators */
.trustIndicators {
  display: flex;
  gap: 24px;
  margin-top: 32px;
  padding-right: 40px;
}

.trustItem {
  text-align: left;
}

.trustNumber {
  font-family: poppins;
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  margin-bottom: 4px;
}

.trustLabel {
  font-family: poppins;
  font-size: 12px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bottomSection {
  display: flex;
  width: 100%;
  position: absolute;
  bottom: 40px;
  left: 60px;
  right: 60px;
  justify-content: flex-end;
  align-items: flex-end;
  z-index: 3;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.socialLinksCont {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.socialLinksCont a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.socialLinksCont a:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Animated Illustration */
.illustrationContainer {
  position: absolute;
  top: 50%;
  right: -20%;
  transform: translateY(-50%);
  width: 600px;
  height: 600px;
  opacity: 0.4;
  pointer-events: none;
  overflow: hidden;
}

.globalNetwork {
  position: relative;
  width: 100%;
  height: 100%;
  animation: float 4s ease-in-out infinite;
}

.networkNode {
  position: absolute;
  width: 18px;
  height: 18px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
}

.networkNode:nth-child(1) {
  top: 20%;
  left: 30%;
  animation-delay: 0s;
}

.networkNode:nth-child(2) {
  top: 40%;
  left: 70%;
  animation-delay: 0.3s;
}

.networkNode:nth-child(3) {
  top: 60%;
  left: 20%;
  animation-delay: 0.6s;
}

.networkNode:nth-child(4) {
  top: 80%;
  left: 60%;
  animation-delay: 0.9s;
}

.networkNode:nth-child(5) {
  top: 30%;
  left: 50%;
  animation-delay: 1.2s;
}

.connectionLine {
  position: absolute;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  transform-origin: left center;
  animation: dataFlow 2s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.4);
}

.connectionLine:nth-child(6) {
  top: 25%;
  left: 35%;
  width: 250px;
  transform: rotate(25deg);
  animation-delay: 0s;
}

.connectionLine:nth-child(7) {
  top: 45%;
  left: 25%;
  width: 220px;
  transform: rotate(-20deg);
  animation-delay: 0.7s;
}

.connectionLine:nth-child(8) {
  top: 65%;
  left: 30%;
  width: 200px;
  transform: rotate(15deg);
  animation-delay: 1.4s;
}

.moneySymbol {
  position: absolute;
  font-size: 32px;
  color: rgba(255, 255, 255, 0.9);
  animation: moneyFloat 3s ease-in-out infinite;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

.moneySymbol:nth-child(9) {
  top: 15%;
  left: 60%;
  animation-delay: 0s;
}

.moneySymbol:nth-child(10) {
  top: 50%;
  left: 80%;
  animation-delay: 1s;
}

.moneySymbol:nth-child(11) {
  top: 75%;
  left: 40%;
  animation-delay: 2s;
}

/* Security Shield Animation */
.securityShield {
  position: absolute;
  top: 10%;
  right: 20%;
  width: 80px;
  height: 80px;
  opacity: 0.6;
  animation: shieldPulse 2s ease-in-out infinite;
}

.securityShield svg {
  width: 100%;
  height: 100%;
  fill: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.4));
}

/* Floating Currency Icons */
.currencyFloat {
  position: absolute;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.7);
  animation: currencyDrift 6s linear infinite;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

.currencyFloat:nth-child(13) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.currencyFloat:nth-child(14) {
  top: 60%;
  left: 90%;
  animation-delay: 2s;
}

.currencyFloat:nth-child(15) {
  top: 80%;
  left: 10%;
  animation-delay: 4s;
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(-50%) translateX(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-50%) translateX(15px) rotate(2deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.4);
  }
}

@keyframes dataFlow {
  0% {
    opacity: 0;
    transform: scaleX(0);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
  100% {
    opacity: 0;
    transform: scaleX(0);
  }
}

@keyframes moneyFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(15deg) scale(1.2);
    opacity: 1;
  }
}

@keyframes shieldPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes currencyDrift {
  0% {
    transform: translateY(100vh) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) rotate(360deg) scale(1.2);
    opacity: 0;
  }
}

/* Hide illustration on mobile */
@media screen and (max-width: 900px) {
  .illustrationContainer {
    display: none;
  }
}
