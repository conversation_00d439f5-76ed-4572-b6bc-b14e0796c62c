/* Simple countdown timer like the existing one */
.countDownTimer {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 18px;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    min-width: 100px;
    text-align: center;
    animation: pulse 2s infinite;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.countDownTimer::before {
    content: '⏱️ ';
    margin-right: 4px;
    font-size: 11px;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}



/* Responsive Design */
@media (max-width: 480px) {
    .countDownTimer {
        font-size: 14px;
        padding: 8px 12px;
        border-radius: 6px;
        min-width: 70px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .countDownTimer {
        animation: none;
    }
}
