"use client";
import React from "react";
import styles from "./accountCard.module.css";

import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { Tooltip } from "react-tooltip";
import { deleteUserPayData } from "@/app/api/userAddPayment/userAddPay";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = ({
  userRecipientPaymentData,
  PayMethodName,
  Accountnum,
  AccountnumLabel,
  bankAccHolder,
  Ifsc,
  id,
  countryName,
  email,
  firstName,
  lastName,
  type,
  ubo_shareholder_date_of_incorporation,
  ubo_shareholder_name,
  payout_option,
  dob,
  currency_payout,
  created_date,
}) => {
  const router = useRouter();

  function shortenWalletAddress(address, startLength = 6, endLength = 4) {
    if (!address) return "";
    if (address.length <= 12) return address; // Don't shorten if already short
    return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
  }

  function shortenEmail(email, maxLength = 18) {
    if (!email) return "";
    if (email.length <= maxLength) return email;

    const [localPart, domain] = email.split('@');
    if (localPart.length > 8) {
      return `${localPart.slice(0, 6)}...@${domain}`;
    }
    return email;
  }

  function shortenAccountNumber(accountNum, maxLength = 16) {
    if (!accountNum) return "";
    if (accountNum.length <= maxLength) return accountNum;

    // For long account numbers, show first 6 and last 4 digits
    return `${accountNum.slice(0, 6)}...${accountNum.slice(-4)}`;
  }

  function formatDate(dateString) {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) return dateString;

      // Format as: May 1, 2025
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };

      return date.toLocaleDateString('en-US', options);
    } catch (error) {
      return dateString; // Return original if formatting fails
    }
  }

  const handleDeleteUserPayments = async () => {
    try {
      const res = await customFetchWithToken.delete(
        `/delete-user-payment-fields-data/${id}`
      );
      toast.success(res.data.message);

      setTimeout(async () => {
        await userPaymentData;
      }, 1000);
    } catch (error) {
      console.log(error);
      toast.error("some Error");
    }
  };
  const handleDeleteRecipient = async () => {
    try {
      const res = await customFetchWithToken.delete(`/delete-recipient/${id}`);
      toast.success(res.data.message);

      setTimeout(async () => {
        await userRecipientPaymentData;
      }, 1000);
    } catch (error) {
      console.log(error);
      toast.error("nada");
    }
  };

  // const handleEditRecipient = async () => {
  //   router.push(
  //     `/pages/addrecipient/${id}?PayMethodName=${PayMethodName}&Accountnum=${Accountnum}&bankAccHolder=${bankAccHolder}&Ifsc=${Ifsc}&id=${id}&countryName=${countryName}&email=${email}&firstName=${firstName}&lastName=${lastName}&type=${type}&ubo_shareholder_date_of_incorporation=${ubo_shareholder_date_of_incorporation}&ubo_shareholder_name=${ubo_shareholder_name}&payout_option=${payout_option}&dob=${dob}&currency_payout=${currency_payout}&created_date=${created_date}&RecipientId=${id}`
  //   );
  // };

  const handleEditRecipient = async () => {
    // if (
    //   !PayMethodName ||
    //   !Accountnum ||
    //   !bankAccHolder ||
    //   !Ifsc ||
    //   !countryName ||
    //   !email ||
    //   !firstName ||
    //   !lastName ||
    //   !type ||
    //   !ubo_shareholder_date_of_incorporation ||
    //   !ubo_shareholder_name ||
    //   !payout_option ||
    //   !dob ||
    //   !currency_payout ||
    //   !created_date
    // ) {
    //   toast.error("Please fill in all fields.");
    //   return;
    // }

    router.push(`/pages/addrecipient/${id}`);
  };

  return (
    <div className={styles.account_container}>
      <div className={styles.accountCards}>
        {/* Header Section with Actions */}
        <div className={styles.cardHeader}>
          <div className={styles.cardTypeSection}>
            <div className={styles.cardTypeLabel}>Account Type</div>
            <div className={styles.cardTypeValue}>{type || "Payment Method"}</div>
          </div>
          <div className={styles.actionButtons}>
            {type && (
              <button
                data-tooltip-id="edit-tooltip"
                data-tooltip-content="Edit Recipient"
                className={`${styles.actionButton} ${styles.editButton}`}
                onClick={handleEditRecipient}
                aria-label="Edit recipient"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                  <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                </svg>
              </button>
            )}
            <button
              data-tooltip-id={type ? "delete-recipient-tooltip" : "delete-payment-tooltip"}
              data-tooltip-content={type ? "Delete Recipient" : "Delete Saved Payment"}
              className={`${styles.actionButton} ${styles.deleteButton}`}
              onClick={type ? handleDeleteRecipient : handleDeleteUserPayments}
              aria-label={type ? "Delete recipient" : "Delete payment method"}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="3,6 5,6 21,6" />
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                <line x1="10" y1="11" x2="10" y2="17" />
                <line x1="14" y1="11" x2="14" y2="17" />
              </svg>
            </button>
          </div>
        </div>

        {/* Card Content - Horizontal Compact Layout */}
        <div className={styles.cardContent}>
          {/* Main Information Grid */}
          <div className={styles.infoGrid}>
            {/* Payment Method */}
            <div className={styles.horizontalField}>
              <span className={styles.fieldLabel}>Method:</span>
              <span className={styles.fieldValue}>{PayMethodName}</span>
            </div>

            {/* Account/Wallet */}
            <div className={styles.horizontalField}>
              <span className={styles.fieldLabel}>
                {PayMethodName && PayMethodName.includes("Crypto") ? "Wallet:" : "Account:"}
              </span>
              <span className={styles.fieldValue} title={Accountnum}>
                {PayMethodName && PayMethodName.includes("Crypto")
                  ? shortenWalletAddress(Accountnum)
                  : shortenAccountNumber(Accountnum)}
              </span>
            </div>

            {/* Country */}
            {countryName && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Country:</span>
                <span className={styles.fieldValue}>{countryName}</span>
              </div>
            )}

            {/* Payout Option */}
            {payout_option && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Payout:</span>
                <span className={styles.fieldValue}>{payout_option}</span>
              </div>
            )}

            {/* First Name */}
            {firstName && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>First Name:</span>
                <span className={styles.fieldValue}>{firstName}</span>
              </div>
            )}

            {/* Last Name */}
            {lastName && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Last Name:</span>
                <span className={styles.fieldValue}>{lastName}</span>
              </div>
            )}

            {/* Email */}
            {email && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Email:</span>
                <span className={styles.fieldValue} title={email}>{shortenEmail(email)}</span>
              </div>
            )}

            {/* Date of Birth */}
            {dob && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>DOB:</span>
                <span className={styles.fieldValue} title={dob}>{formatDate(dob)}</span>
              </div>
            )}

            {/* UBO Shareholder */}
            {ubo_shareholder_name && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>UBO:</span>
                <span className={styles.fieldValue}>{ubo_shareholder_name}</span>
              </div>
            )}

            {/* Incorporation Date */}
            {ubo_shareholder_date_of_incorporation && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Inc. Date:</span>
                <span className={styles.fieldValue} title={ubo_shareholder_date_of_incorporation}>{formatDate(ubo_shareholder_date_of_incorporation)}</span>
              </div>
            )}

            {/* Currency */}
            {currency_payout && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Currency:</span>
                <span className={styles.fieldValue}>{currency_payout}</span>
              </div>
            )}

            {/* Created Date */}
            {created_date && (
              <div className={styles.horizontalField}>
                <span className={styles.fieldLabel}>Created:</span>
                <span className={styles.fieldValue} title={created_date}>{formatDate(created_date)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tooltips */}
      <Tooltip
        id="edit-tooltip"
        style={{
          backgroundColor: "#1a1a1a",
          color: "#ffffff",
          fontSize: "12px",
          borderRadius: "6px",
          padding: "8px 12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        }}
      />
      <Tooltip
        id="delete-recipient-tooltip"
        style={{
          backgroundColor: "#1a1a1a",
          color: "#ffffff",
          fontSize: "12px",
          borderRadius: "6px",
          padding: "8px 12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        }}
      />
      <Tooltip
        id="delete-payment-tooltip"
        style={{
          backgroundColor: "#1a1a1a",
          color: "#ffffff",
          fontSize: "12px",
          borderRadius: "6px",
          padding: "8px 12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        }}
      />
      {/* ToastContainer removed - already provided globally in LayoutWrapper.js */}
    </div>
  );
};

export default page;
