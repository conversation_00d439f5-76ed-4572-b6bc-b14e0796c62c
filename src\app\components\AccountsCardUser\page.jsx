"use client";
import { useState, useEffect } from "react";
import styles from "./accountCard.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { Tooltip } from "react-tooltip";
import { deleteUserPayData } from "@/app/api/userAddPayment/userAddPay";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import AccountsEditPay from "../../components/EditAccountsAddPay/page";

const page = ({ type, id, item: initialItem, method, userPaymentData }) => {
  const router = useRouter();
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const [showAddPaymentComp, setShowAddPaymentComp] = useState(false);
  const [item, setItem] = useState(initialItem);
  const [currentMethod, setCurrentMethod] = useState(method);
  const [payDetails, setPayDetails] = useState([]);
  function shortenWalletAddress(address, startLength = 8, endLength = 6) {
    if (!address) return "";
    return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
  }

  const handleDeleteUserPayments = async () => {
    try {
      const res = await customFetchWithToken.delete(
        `/delete-user-payment-fields-data/${id}`
      );
      toast.success(res.data.message);
      setTimeout(async () => {
        await userPaymentData();
      }, 1000);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const fetchPayment = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/get-user-payment-fields-data/${id}`
      );

      setCurrentMethod(res.data.data.payment_method);
      setPayDetails(res.data.data.data);

      // Update the item state with the latest data from API
      setItem(res.data.data.data);
    } catch (error) {
      console.error("Error fetching payment details:", error);
    }
  };

  const handleEditPayment = async () => {
    setShowAddPaymentModal(!showAddPaymentModal);
    setShowAddPaymentComp(true);
    try {
      const res = await customFetchWithToken.get(
        `/get-user-payment-fields-data/${id}`
      );

      setCurrentMethod(res.data.data.payment_method);
      setPayDetails(res.data.data.data);
      toast.success(res.data.message);
    } catch (error) {
      console.log(error);
    }
  };

  const handleState = async (data) => {
    setShowAddPaymentComp(data);
    if (!data) {
      // When closing edit mode, only refresh this individual card's data
      await fetchPayment();
    }
  };

  // Update item when initialItem changes (after parent refresh)
  useEffect(() => {
    setItem(initialItem);
  }, [initialItem]);

  if (showAddPaymentComp) {
    return (
      <div className={styles.addPayMethod}>
        <AccountsEditPay id={id} showToast={false} stateFunc={handleState} />
      </div>
    );
  }

  return (
    <div className={styles.account_container}>
      <div className={styles.accountCards}>
        {/* Card Header with Payment Method Badge */}
        <div className={styles.cardHeader}>
          <div className={styles.paymentMethodBadge}>
            <span className={styles.badgeIcon}>💳</span>
            <span className={styles.badgeText}>{currentMethod}</span>
          </div>

          {!type && (
            <div className={styles.actionButtons}>
              <button
                data-tooltip-id="edit-tooltip"
                data-tooltip-content="Edit Payment Method"
                className={`${styles.actionBtn} ${styles.editBtn}`}
                onClick={handleEditPayment}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              <button
                data-tooltip-id="delete-tooltip"
                data-tooltip-content="Delete Payment Method"
                className={`${styles.actionBtn} ${styles.deleteBtn}`}
                onClick={handleDeleteUserPayments}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M3 6h18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Card Content */}
        <div className={styles.cardContent}>
          <div className={styles.paymentDetails}>
            {item && item.length > 0 ? (
              item.map((el, index) => (
                <div key={index} className={styles.detailItem}>
                  <div className={styles.detailLabel}>{el.key}</div>
                  <div className={styles.detailValue}>
                    {(typeof el.value === "string" && el.value.startsWith("0x")) ||
                    (el.value && el.value.length > 20)
                      ? shortenWalletAddress(el.value)
                      : el.value || "N/A"}
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noDetails}>
                <span className={styles.noDetailsIcon}>📄</span>
                <span>No payment details available</span>
              </div>
            )}
          </div>
        </div>

        {/* Card Footer */}
        <div className={styles.cardFooter}>
          <div className={styles.statusIndicator}>
            <span className={styles.statusDot}></span>
            <span className={styles.statusText}>Active</span>
          </div>
          <div className={styles.cardId}>ID: {id}</div>
        </div>
      </div>

      <Tooltip id="edit-tooltip" />
      <Tooltip id="delete-tooltip" />
   
    </div>
  );
};

export default page;
