/* Import Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Modern Alert Popup */
.alertPopup {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    margin: 16px 0;
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease-out;
}

.alertPopup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #ef4444, #f59e0b);
    border-radius: 16px 16px 0 0;
}

.alertPopup p {
    font-size: 15px;
    font-weight: 500;
    color: #1e293b;
    margin: 0 0 20px 0;
    line-height: 1.5;
    text-align: center;
}

/* Button Container */
.btnContainer {
    display: flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
    width: 100%;
}

/* Yes Button - Success Style */
.yesBtn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 10px;
    border: none;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 80px;
}

.yesBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 10px;
    pointer-events: none;
}

.yesBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.yesBtn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* No Button - Danger Style */
.noBtn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 10px;
    border: none;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 80px;
}

.noBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 10px;
    pointer-events: none;
}

.noBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.noBtn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Horizontal Trade Timer Container */
.tradeTimerContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
}

/* Timer Section - Horizontal Layout */
.timerSection {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.timerSection::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ef4444, #dc2626, #ef4444);
    border-radius: 12px 12px 0 0;
}

.timerLabel {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 15px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.timerLabel::before {
    content: '⏰';
    font-size: 16px;
}

/* Modern Countdown Timer - Horizontal Version */
.countDownTimer {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 18px;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    min-width: 100px;
    text-align: center;
    animation: pulse 2s infinite;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.countDownTimer::before {
    content: '⏱️ ';
    margin-right: 4px;
    font-size: 11px;
}

.countDownTimer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 8px;
    pointer-events: none;
}

/* Pulse animation for urgency */
@keyframes pulse {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(239, 68, 68, 0.5);
    }
}

/* Animation for popup */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .alertPopup {
        padding: 20px;
        margin: 12px 0;
        border-radius: 12px;
    }

    .alertPopup p {
        font-size: 14px;
        margin-bottom: 16px;
    }

    .btnContainer {
        gap: 10px;
    }

    .yesBtn,
    .noBtn {
        padding: 10px 20px;
        font-size: 13px;
        min-width: 70px;
    }

    .timerSection {
        padding: 12px 16px;
        border-radius: 10px;
    }

    .timerLabel {
        font-size: 14px;
    }

    .timerLabel::before {
        font-size: 14px;
    }

    .tradeTimerContainer {
        margin-bottom: 16px;
    }

    .countDownTimer {
        font-size: 16px;
        padding: 10px 16px;
        border-radius: 8px;
        min-width: 80px;
    }
}

@media (max-width: 480px) {
    .alertPopup {
        padding: 16px;
        margin: 8px 0;
        border-radius: 10px;
    }

    .alertPopup p {
        font-size: 13px;
        margin-bottom: 14px;
    }

    .btnContainer {
        gap: 8px;
        flex-direction: column;
    }

    .yesBtn,
    .noBtn {
        padding: 12px 20px;
        font-size: 13px;
        width: 100%;
        min-width: unset;
    }

    .timerSection {
        padding: 10px 12px;
        border-radius: 8px;
    }

    .timerLabel {
        font-size: 13px;
    }

    .timerLabel::before {
        font-size: 13px;
    }

    .tradeTimerContainer {
        margin-bottom: 12px;
    }

    .countDownTimer {
        font-size: 14px;
        padding: 8px 12px;
        border-radius: 6px;
        min-width: 70px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .alertPopup,
    .yesBtn,
    .noBtn {
        animation: none;
        transition: none;
    }

    .yesBtn:hover,
    .noBtn:hover {
        transform: none;
    }
}

/* Focus states for accessibility */
.yesBtn:focus-visible,
.noBtn:focus-visible {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .alertPopup {
        border: 2px solid #000000;
        background: #ffffff;
    }

    .countDownTimer {
        border: 2px solid #000000;
    }

    .yesBtn,
    .noBtn {
        border: 2px solid #000000;
    }
}