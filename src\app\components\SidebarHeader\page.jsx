"use client";
import Image from "next/image";
import styles from "./sidebarHeader.module.css";
import ProfileImg from "../../../../public/assets/profile/profileImg.png";
import arrow from "../../../../public/assets/profile/arrow.png";
import { FaCheckCircle, FaExclamationCircle, FaHourglassHalf, FaUserShield, FaChevronDown } from "react-icons/fa";
import { useState, useEffect } from "react";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const STATUS_CONFIG = {
  "User_Detail": {
    label: "Registration Pending",
    shortLabel: "Registering",
    icon: FaHourglassHalf,
    variant: "warning",
    priority: 1
  },
  "Document-Verifications": {
    label: "KYC Verification Required",
    shortLabel: "KYC Pending",
    icon: FaExclamationCircle,
    variant: "pending",
    priority: 2
  },
  "Admin-Verifications": {
    label: "Administrative Review",
    shortLabel: "Under Review",
    icon: FaUserShield,
    variant: "review",
    priority: 3
  },
  "Dash_Board": {
    label: "Fully Verified",
    shortLabel: "Verified",
    icon: FaCheckCircle,
    variant: "success",
    priority: 4
  },
};

const SidebarHeader = ({ token, status, handleRedirect }) => {
  const [profilePic, setProfilePic] = useState("");
  const [firstName, setFirstName] = useState("");
  let localToken;
  
  if (typeof window !== "undefined") {
    localToken = sessionStorage.getItem("user");
  }

  const isAuthenticated = token || localToken;
  const currentStatus = STATUS_CONFIG[status];
  const StatusIcon = currentStatus?.icon;

  // Fetch profile info
  const getProfileInfo = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");
      setProfilePic(res.data.data.img_logo);
      setFirstName(res.data.data.firstname);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      getProfileInfo();
    }
  }, [isAuthenticated]);

  return (
    <header className={styles.headerContainer} role="banner">
      {/* Brand Section */}
      <section className={styles.brandSection}>
        <div className={styles.logoWrapper}>
          <div className={styles.logoContainer}>
            <h1 className={styles.brandText}>Remflow</h1>
          </div>
          <div className={styles.brandAccent}></div>
        </div>
      </section>

      {/* Profile & Status Section */}
      {isAuthenticated && (
        <section className={styles.profileSection}>
          <div className={styles.profileCard}>
            {/* User Avatar */}
            <div className={styles.avatarContainer}>
              <div className={styles.avatarWrapper}>
                <Image
                  unoptimized={true}
                  src={profilePic || "/default-avatar.png"}
                  alt="User Profile"
                  width={34}
                  height={34}
                  className={styles.avatar}
                />
                <div className={`${styles.statusIndicator} ${styles[`indicator--${currentStatus?.variant}`]}`}>
                  <div className={styles.statusDot}></div>
                </div>
              </div>
            </div>

            {/* Status Information */}
            <div className={styles.statusContainer}>
              <span className={styles.statusLabel}>Welcome, {firstName || 'User'}</span>
              
              {currentStatus && (
                <button 
                  className={`${styles.statusBadge} ${styles[`badge--${currentStatus.variant}`]}`}
                  onClick={handleRedirect}
                  aria-label={`Current status: ${currentStatus.label}. Click for details.`}
                  type="button"
                >
                  <div className={styles.badgeContent}>
                    <StatusIcon className={styles.badgeIcon} aria-hidden="true" />
                    Status:
                    <span className={styles.badgeText}>
                      <span className={styles.badgeTextFull}>{currentStatus.label}</span>
                      <span className={styles.badgeTextShort}>{currentStatus.shortLabel}</span>
                    </span>
                    <div className={styles.badgePriority} data-priority={currentStatus.priority}></div>
                  </div>
                </button>
              )}
            </div>

            {/* Profile Menu Toggle */}
            {/* <button 
              className={styles.menuToggle}
              aria-label="Open profile menu"
              type="button"
            >
              <FaChevronDown className={styles.menuIcon} aria-hidden="true" />
            </button> */}
          </div>
        </section>
      )}
    </header>
  );
};

export default SidebarHeader;
