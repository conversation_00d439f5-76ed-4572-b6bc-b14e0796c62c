import axios from "axios";

const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

export const getUserAddPayList = async () => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const response = await axios({
    url: `${BaseURL}/get-payment-list/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return response;
};

export const addPaymentUser = async (paymentId, formData) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const response = await axios({
    url: `${BaseURL}/user-payment-fields-data/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "POST",
    data: {
      payment_list: `${paymentId}`,
      data: formData,
    },
  });
  return response;
};

export const getUserPayData = async () => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const response = await axios({
    url: `${BaseURL}/user-payment-fields-data/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return response;
};
export const deleteUserPayData = async (id) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const response = await axios({
    url: `${BaseURL}/delete-user-payment-fields-data/${id}`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "DELETE",
  });
  return response;
};

export const getAddPayFields = async (method, currencyFrom) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const response = await axios({
    url: `${BaseURL}/user-payment-fields/?payment_method=${method}&currency=${currencyFrom}`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return response;
};
