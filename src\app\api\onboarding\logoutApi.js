import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;

const logoutApi = async () => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
    console.log("ttokenLogout", token);
  }

  const response = await axios({
    url: `${Base_url}/destroy-user-token/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response;
};

export default logoutApi;
