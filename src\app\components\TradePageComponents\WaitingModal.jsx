import React from 'react';
import Modal from "react-modal";
import styles from '../../pages/trade/search_results.module.css';

const WaitingModal = ({ 
  showWaitingModal, 
  orderNumber, 
  tradeData, 
  handleCancelTrade 
}) => {
  const modalStyles = {
    content: {
      position: "relative",
      top: "auto",
      left: "auto",
      right: "auto",
      bottom: "auto",
      border: "none",
      background: "white",
      borderRadius: "15px",
      outline: "none",
      padding: "30px",
      width: "450px",
      maxWidth: "90vw",
      textAlign: "center",
      margin: "0",
      transform: "none",
    },
    overlay: {
      position: "fixed",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      zIndex: 99999,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      padding: "20px",
    }
  };

  return (
    <Modal
      className={styles.modalWrapper}
      isOpen={showWaitingModal}
      style={modalStyles}
      contentLabel="Waiting for Peer Acceptance"
      shouldCloseOnOverlayClick={false}
      shouldCloseOnEsc={false}
    >
      <div className={styles.waitingModalContent}>
        <div className={styles.waitingIcon}>
          <svg
            width="60"
            height="60"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={styles.spinningIcon}
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="#7939EE"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray="31.416"
              strokeDashoffset="31.416"
              fill="none"
            >
              <animate
                attributeName="stroke-dasharray"
                dur="2s"
                values="0 31.416;15.708 15.708;0 31.416;0 31.416"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-dashoffset"
                dur="2s"
                values="0;-15.708;-31.416;-31.416"
                repeatCount="indefinite"
              />
            </circle>
          </svg>
        </div>
        
        <h2 className={styles.waitingTitle}>Waiting for Peer Acceptance</h2>
        
        <p className={styles.waitingMessage}>
          Your trade request has been sent to the peer. Please wait while they review and accept your trade.
        </p>
        
        <div className={styles.waitingDetails}>
          <div className={styles.waitingDetailItem}>
            <strong>Order Number:</strong> {orderNumber}
          </div>
          <div className={styles.waitingDetailItem}>
            <strong>Amount:</strong> {tradeData?.trade_amount} {tradeData?.currency_from}
          </div>
          {/* <div className={styles.waitingDetailItem}>
            <strong>Peer:</strong> {tradeData?.flag === "user"
              ? tradeData?.peer_details?.username
              : tradeData?.user_details?.username}
          </div> */}
        </div>
        
        <div className={styles.waitingNote}>
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="12" cy="12" r="10" stroke="#7939EE" strokeWidth="2"/>
            <path d="m9 12 2 2 4-4" stroke="#7939EE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          This page will automatically update when the peer accepts your trade.
        </div>
        
        <div className={styles.waitingModalActions}>
          <button
            className={styles.waitingCancelBtn}
            onClick={handleCancelTrade}
          >
            Cancel Trade
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default WaitingModal;
