/* Import Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

.reviewModalContainer {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
    margin: 0 auto;
    overflow: hidden;
    font-family: 'Poppins', sans-serif;
    position: relative;
    z-index: 100000;
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background-color: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.closeButton {
    cursor: pointer;
    font-size: 20px;
    color: #6e7191;
    transition: all 0.2s ease;
}

.closeButton:hover {
    color: #14142b;
    transform: scale(1.1);
}

.ratingContainer {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
}

.starsContainer {
    display: flex;
    justify-content: center;
    margin: 18px 0;
}

.star {
    font-size: 32px;
    margin: 0 6px;
    cursor: pointer;
    color: #E0E0E0;
    transition: all 0.3s ease;
}

.star:hover {
    transform: scale(1.15);
}

.filledStar {
    color: #FFD700;
}

.reviewContainer {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;
}

.reviewTextarea {
    width: 100%;
    min-height: 100px;
    padding: 14px;
    border: 1px solid #e7e7e9;
    border-radius: 10px;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    resize: vertical;
    margin-top: 12px;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

.reviewTextarea:focus {
    outline: none;
    border-color: #4153ed;
    box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.buttonContainer {
    display: flex;
    justify-content: space-between;
    padding: 20px 24px;
    gap: 14px;
}

.submitButton {
    background-color: #4153ed;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    box-shadow: 0 4px 10px rgba(65, 83, 237, 0.25);
}

.submitButton:hover {
    background-color: #3142d0;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(65, 83, 237, 0.3);
}

.submitButton:active {
    transform: translateY(0);
}

.submitButton:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.cancelButton {
    background-color: transparent;
    color: #6e7191;
    border: 1px solid #e7e7e9;
    border-radius: 10px;
    padding: 12px 24px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.cancelButton:hover {
    background-color: #f7f7fc;
    color: #14142b;
}

.cancelButton:active {
    background-color: #efefef;
}

.cancelButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

@media (max-width: 600px) {
    .reviewModalContainer {
        width: 95%;
    }

    .buttonContainer {
        flex-direction: column;
        gap: 12px;
    }

    .submitButton,
    .cancelButton {
        width: 100%;
    }
}