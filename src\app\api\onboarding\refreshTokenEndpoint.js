import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
// if (typeof window !== "undefined") {
//   var refreshToken = localStorage.getItem("refreshToken");
// }

const refreshTokenApi = async () => {
  let refreshToken = localStorage.getItem("refreshToken");

  const response = await axios({
    url: `${Base_url}/get-access-token/?refresh_token=${refreshToken}`,
    method: "GET",
  });
  return response;
};

export default refreshTokenApi;
