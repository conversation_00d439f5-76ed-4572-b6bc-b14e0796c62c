@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* ===== HEADER CONTAINER ===== */
.headerContainer {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%);
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: none;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.headerContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.headerContainer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* ===== BRAND SECTION ===== */
.brandSection {
  padding: 4px 2px 6px;
  position: relative;
  z-index: 2;
}

.logoWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.logoContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logoContainer:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.brandLogo {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.brandText {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  padding: 1px 36px;
  text-align: center;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.logoContainer:hover .brandText {
  transform: scale(1.02);
  filter: brightness(1.1);
}

.brandAccent {
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #60a5fa, #a78bfa, #f472b6);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.4);
}

/* ===== PROFILE SECTION ===== */
.profileSection {
  padding: 0 16px 20px;
  position: relative;
  z-index: 2;
}

.profileCard {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.profileCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.profileCard:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.18);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* ===== AVATAR ===== */
.avatarContainer {
  flex-shrink: 0;
}

.avatarWrapper {
  position: relative;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  padding: 2px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatarWrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.avatar {
  width: 34px;
  height: 34px;
  min-width: 34px;
  min-height: 34px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.statusIndicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #1e1b4b;
  background: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.statusDot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  animation: pulse 2s infinite;
}

.indicator--success { background: #10b981; }
.indicator--pending { background: #f59e0b; }
.indicator--warning { background: #ef4444; }
.indicator--review { background: #3b82f6; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===== STATUS CONTAINER ===== */
.statusContainer {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.statusLabel {
  font-family: 'Poppins', sans-serif;
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1;
}

/* ===== STATUS BADGE ===== */
.statusBadge {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  outline: none;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.statusBadge:focus-visible {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
}

.badgeContent {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  font-weight: 600;
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.badgeIcon {
  font-size: 10px;
  flex-shrink: 0;
}

.badgeText {
  min-width: 0;
  position: relative;
}

.badgeTextFull {
  display: block;
}

.badgeTextShort {
  display: none;
}

.badgePriority {
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  border-radius: 2px;
  opacity: 0.6;
}

/* Badge Variants */
.badge--success .badgeContent {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.badge--success .badgePriority {
  background: #34d399;
}

.badge--pending .badgeContent {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.badge--pending .badgePriority {
  background: #fbbf24;
}

.badge--warning .badgeContent {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.badge--warning .badgePriority {
  background: #f87171;
}

.badge--review .badgeContent {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.badge--review .badgePriority {
  background: #60a5fa;
}

.statusBadge:hover .badgeContent {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

.statusBadge:active .badgeContent {
  transform: translateY(0);
}

/* ===== MENU TOGGLE ===== */
.menuToggle {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.menuToggle:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.menuToggle:focus-visible {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
}

.menuIcon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.menuToggle:hover .menuIcon {
  color: #ffffff;
  transform: translateY(1px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 1024px) {
  .badgeTextFull {
    display: none;
  }
  
  .badgeTextShort {
    display: block;
  }
  
  .profileCard {
    padding: 14px;
    gap: 10px;
  }
}

@media screen and (max-width: 768px) {
  .headerContainer {
    border-radius: 16px 16px 0 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: none;
  }
  
  .brandSection {
    padding: 20px 16px 14px;
  }
  
  .brandText {
    font-size: 20px;
    padding: 6px 12px;
  }
  
  .profileSection {
    padding: 0 12px 16px;
  }
  
  .profileCard {
    padding: 12px;
    gap: 8px;
  }
  
  .avatarWrapper {
    width: 36px;
    height: 36px;
  }
  
  .statusIndicator {
    width: 14px;
    height: 14px;
  }
  
  .statusDot {
    width: 5px;
    height: 5px;
  }
  
  .badgeContent {
    padding: 6px 12px;
    font-size: 11px;
  }
  
  .menuToggle {
    width: 28px;
    height: 28px;
  }
  
  .menuIcon {
    font-size: 10px;
  }
}

@media screen and (max-width: 576px) {
  .headerContainer {
    display: none;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .headerContainer,
  .logoContainer,
  .avatarWrapper,
  .statusBadge,
  .badgeContent,
  .menuToggle,
  .profileCard {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .statusDot {
    animation: none !important;
  }
}

@media (prefers-contrast: high) {
  .headerContainer {
    border: 2px solid #ffffff;
  }
  
  .profileCard {
    border: 1px solid #ffffff;
  }
  
  .statusBadge .badgeContent {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}
