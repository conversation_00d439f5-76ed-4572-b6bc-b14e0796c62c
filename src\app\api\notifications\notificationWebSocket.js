// JAVED's CODE 👇
import { useEffect, useState, useCallback, useRef } from "react";

export const isBrowser = typeof window !== "undefined";

export const useWebSocket = (url) => {
  const authTokenRef = useRef(null);
  const [wsInstance, setWsInstance] = useState(null);

  if (typeof window !== "undefined") {
    const token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }
  const protocols = [authTokenRef.current];
  console.log("TTT", authTokenRef);
  const updateWs = useCallback(
    (url) => {
      if (!isBrowser) return setWsInstance(null);

      if (wsInstance?.readyState !== 3) {
        wsInstance.close();
      }

      const newWs = new WebSocket(`${url}?token=${authTokenRef.current}`);
      setWsInstance(newWs);
    },
    [wsInstance]
  );
  useEffect(() => {
    if (isBrowser) {
      const ws = new WebSocket(`${url}?token=${authTokenRef.current}`);
      setWsInstance(ws);
    }

    return () => {
      if (wsInstance && wsInstance.readyState !== 3) {
        wsInstance.close();
      }
    };
  }, []);

  return { wsInstance, updateWs };
};
// JAVED's CODE 👆
// import React, { useEffect, useRef } from "react";
// import WebSocket from "websocket";
// const WebSocketComponent = () => {
//   if (typeof window !== "undefined") {
//     const token = sessionStorage.getItem("user");
//     if (token) {
//       authTokenRef.current = token;
//     }
//   }
//   useEffect(() => {
//     const url = "ws://rem.remflow.net/ws/transaction/";
//     const token = authTokenRef.current;
//     let socket = null;
//     const connectWebSocket = () => {
//       socket = new WebSocket(url);
//       socket.onopen = () => {
//         // Send authentication token
//         socket.send(JSON.stringify({ token }));
//       };
//       socket.onmessage = (event) => {
//         // Handle incoming messages
//         console.log("Received message:", event.data);
//       };
//       socket.onclose = () => {
//         console.log("Connection closed");
//       };
//       socket.onerror = (error) => {
//         console.error("WebSocket error:", error);
//       };
//     };
//     connectWebSocket();
//     return () => {
//       if (socket) {
//         socket.close();
//       }
//     };
//   }, []);
//   return (
//     <div>
//       <h1>WebSocket Component</h1>
//     </div>
//   );
// };
// export default WebSocketComponent;
