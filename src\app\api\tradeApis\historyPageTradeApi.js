import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const getAllTradeReqApi = async () => {
  const res = await axios({
    url: `${Base_url}/get-trade-request/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const getAllPeerTradeReqApi = async (status) => {
  const res = await axios({
    url: `${Base_url}/get-trade-request/?type=peer`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const getAllSenderTradeReqApi = async (status) => {
  const res = await axios({
    url: `${Base_url}/get-trade-request/?type=sender`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const getAllTradeReqStatusApi = async (status) => {
  const res = await axios({
    url: `${Base_url}/get-trade-request/?status=${status}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const getTradeById = async (id) => {
  const res = await axios({
    url: `${Base_url}/trade-by-id/?order_id=${id}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const getTradeByOrderNo = async (order_number) => {
  const res = await axios({
    url: `${Base_url}/get-trade-request/?order_number=${order_number}&status=`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
