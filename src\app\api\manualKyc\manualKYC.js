import axios from "axios";
import { apiHandlerWrapper } from "@/app/utils/apiHandlerWrapper";
require("dotenv").config();

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

const Base_URL = process.env.NEXT_PUBLIC_Base_URL;
export const manualKycApi = async (Data) => {
  const res = await axios({
    url: `${Base_URL}/upload-document-cleardil/`,
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "multipart/form-data",
    },
    method: "POST",
    data: Data,
  });
  return res;
};

export const generateKycLink = async () => {
  const res = apiHandlerWrapper("kyc-url-generate", "POST");
  return res;
};
export const checkKycStatus = async () => {
  const res = apiHandlerWrapper("kyc-status-check", "GET");
  return res;
};
